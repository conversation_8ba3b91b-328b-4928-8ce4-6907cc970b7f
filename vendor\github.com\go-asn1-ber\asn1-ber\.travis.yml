language: go

go:
  - 1.2.x
  - 1.6.x
  - 1.9.x
  - 1.10.x
  - 1.11.x
  - 1.12.x
  - 1.14.x
  - tip

os:
  - linux

arch:
  - amd64

dist: xenial

env:
  - GOARCH=amd64

jobs:
  include:
    - os: windows
      go: 1.14.x
    - os: osx
      go: 1.14.x
    - os: linux
      go: 1.14.x
      arch: arm64
    - os: linux
      go: 1.14.x
      env:
        - GOARCH=386

script:
  - go test -v -cover ./... || go test -v ./...
