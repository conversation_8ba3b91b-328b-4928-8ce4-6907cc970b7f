[![](https://godoc.org/github.com/jackc/pgproto3?status.svg)](https://godoc.org/github.com/jackc/pgproto3)
[![Build Status](https://travis-ci.org/jackc/pgproto3.svg)](https://travis-ci.org/jackc/pgproto3)

# pgproto3

Package pgproto3 is a encoder and decoder of the PostgreSQL wire protocol version 3.

pgproto3 can be used as a foundation for PostgreSQL drivers, proxies, mock servers, load balancers and more.

See example/pgfortune for a playful example of a fake PostgreSQL server.

Extracted from original implementation in https://github.com/jackc/pgx.
