List of all the awesome people working to make Gin the best Web Framework in Go.

## gin 1.x series authors

**Gin Core Team:** <PERSON><PERSON><PERSON> (@appleboy), <PERSON>欧 (@thinkerou), <PERSON> (@javierprovecho)

## gin 0.x series authors

**Maintainers:** <PERSON><PERSON> (@manucorporat), <PERSON> (@javierprovecho)

People and companies, who have contributed, in alphabetical order.

**@858806258 (杰哥)**
- Fix typo in example


**@ached<PERSON><PERSON><PERSON> (Klemen Sever)**
- Fix newline debug printing


**@adammck (<PERSON>)**
- Add MIT license


**@AlexanderChen1989 (<PERSON>)**
- Typos in README


**@alexa<PERSON><PERSON><PERSON><PERSON> (<PERSON>)**
- Add support multipart/form-data


**@alexa<PERSON><PERSON><PERSON> (<PERSON>)**
- Using template.Must to fix multiple return issue
- ★ Added support for OPTIONS verb
- ★ Setting response headers before calling WriteHeader
- Improved documentation for model binding
- ★ Added Content.Redirect()
- ★ Added tons of Unit tests


**@austinheap (Austin Heap)**
- Added travis <PERSON> integration


**@and<PERSON><PERSON><PERSON> (Andre <PERSON>)**
- Fix typo in comment


**@<PERSON><PERSON> (Ludwig <PERSON> Vasquez)**
- Fix html templating in debug mode


**@bluele (Jun Kimura)**
- Fixes code examples in README


**@chad-russell**
- ★ Support for serializing gin.H into XML


**@dickeyxxx (Jeff Dickey)**
- Typos in README
- Add example about serving static files


**@donileo (Adonis)**
- Add NoMethod handler


**@dutchcoders (DutchCoders)**
- ★ Fix security bug that allows client to spoof ip
- Fix typo. r.HTMLTemplates -> SetHTMLTemplate


**@el3ctro- (Joshua Loper)**
- Fix typo in example


**@ethankan (Ethan Kan)**
- Unsigned integers in binding


**(Evgeny Persienko)**
- Validate sub structures


**@frankbille (Frank Bille)**
- Add support for HTTP Realm Auth


**@fmd (Fareed Dudhia)**
- Fix typo. SetHTTPTemplate -> SetHTMLTemplate


**@ironiridis (Christopher Harrington)**
- Remove old reference


**@jammie-stackhouse (Jamie Stackhouse)**
- Add more shortcuts for router methods


**@jasonrhansen**
- Fix spelling and grammar errors in documentation


**@JasonSoft (Jason Lee)**
- Fix typo in comment


**@joiggama (Ignacio Galindo)**
- Add utf-8 charset header on renders


**@julienschmidt (Julien Schmidt)**
- gofmt the code examples


**@kelcecil (Kel Cecil)**
- Fix readme typo


**@kyledinh (Kyle Dinh)**
- Adds RunTLS()


**@LinusU (Linus Unnebäck)**
- Small fixes in README


**@loongmxbt (Saint Asky)**
- Fix typo in example


**@lucas-clemente (Lucas Clemente)**
- ★ work around path.Join removing trailing slashes from routes


**@mattn (Yasuhiro Matsumoto)**
- Improve color logger


**@mdigger (Dmitry Sedykh)**
- Fixes Form binding when content-type is x-www-form-urlencoded
- No repeat call c.Writer.Status() in gin.Logger
- Fixes Content-Type for json render


**@mirzac (Mirza Ceric)**
- Fix debug printing


**@mopemope (Yutaka Matsubara)**
- ★ Adds Godep support (Dependencies Manager)
- Fix variadic parameter in the flexible render API
- Fix Corrupted plain render
- Add Pluggable View Renderer Example
 

**@msemenistyi (Mykyta Semenistyi)**
- update Readme.md. Add code to String method


**@msoedov (Sasha Myasoedov)**
- ★ Adds tons of unit tests.


**@ngerakines (Nick Gerakines)**
- ★ Improves API, c.GET() doesn't panic
- Adds MustGet() method


**@r8k (Rajiv Kilaparti)**
- Fix Port usage in README.


**@rayrod2030 (Ray Rodriguez)**
- Fix typo in example


**@rns**
- Fix typo in example


**@RobAWilkinson (Robert Wilkinson)**
- Add example of forms and params


**@rogierlommers (Rogier Lommers)**
- Add updated static serve example


**@se77en (Damon Zhao)**
- Improve color logging


**@silasb (Silas Baronda)**
- Fixing quotes in README


**@SkuliOskarsson (Skuli Oskarsson)**
- Fixes some texts in README II


**@slimmy (Jimmy Pettersson)**
- Added messages for required bindings


**@smira (Andrey Smirnov)**
- Add support for ignored/unexported fields in binding


**@superalsrk (SRK.Lyu)**
- Update httprouter godeps


**@tebeka (Miki Tebeka)**
- Use net/http constants instead of numeric values


**@techjanitor**
- Update context.go reserved IPs


**@yosssi (Keiji Yoshida)**
- Fix link in README


**@yuyabee**
- Fixed README
