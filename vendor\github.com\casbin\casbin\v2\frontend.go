// Copyright 2020 The casbin Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package casbin

import (
	"encoding/json"
)

func CasbinJsGetPermissionForUser(e IEnforcer, user string) ([]byte, error) {
	policy, err := e.GetImplicitPermissionsForUser(user)
	if err != nil {
		return nil, err
	}
	permission := make(map[string][]string)
	for i := 0; i < len(policy); i++ {
		permission[policy[i][2]] = append(permission[policy[i][2]], policy[i][1])
	}
	b, _ := json.Marshal(permission)
	return b, nil
}
