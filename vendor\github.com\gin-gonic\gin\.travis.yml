language: go

matrix:
  fast_finish: true
  include:
  - go: 1.11.x
    env: GO111MODULE=on
  - go: 1.12.x
    env: GO111MODULE=on
  - go: 1.13.x
  - go: 1.13.x
    env: 
      - TESTTAGS=nomsgpack
  - go: 1.14.x
  - go: 1.14.x
    env:
      - TESTTAGS=nomsgpack
  - go: master

git:
  depth: 10

before_install:
  - if [[ "${GO111MODULE}" = "on" ]]; then mkdir "${HOME}/go"; export GOPATH="${HOME}/go"; fi

install:
  - if [[ "${GO111MODULE}" = "on" ]]; then go mod download; fi
  - if [[ "${GO111MODULE}" = "on" ]]; then export PATH="${GOPATH}/bin:${GOROOT}/bin:${PATH}"; fi
  - if [[ "${GO111MODULE}" = "on" ]]; then make tools; fi

go_import_path: github.com/gin-gonic/gin

script:
  - make vet
  - make fmt-check
  - make misspell-check
  - make test

after_success:
  - bash <(curl -s https://codecov.io/bash)

notifications:
  webhooks:
    urls:
      - https://webhooks.gitter.im/e/7f95bf605c4d356372f4
    on_success: change  # options: [always|never|change] default: always
    on_failure: always  # options: [always|never|change] default: always
    on_start: false     # default: false
