[![GoDoc](https://godoc.org/gopkg.in/asn1-ber.v1?status.svg)](https://godoc.org/gopkg.in/asn1-ber.v1) [![Build Status](https://travis-ci.org/go-asn1-ber/asn1-ber.svg)](https://travis-ci.org/go-asn1-ber/asn1-ber)


ASN1 BER Encoding / Decoding Library for the GO programming language.
---------------------------------------------------------------------

Required libraries: 
   None

Working:
   Very basic encoding / decoding needed for LDAP protocol

Tests Implemented:
   A few

TODO:
   Fix all encoding / decoding to conform to ASN1 BER spec
   Implement Tests / Benchmarks

---

The Go gopher was designed by <PERSON>. (http://reneefrench.blogspot.com/)
The design is licensed under the Creative Commons 3.0 Attributions license.
Read this article for more details: http://blog.golang.org/gopher
