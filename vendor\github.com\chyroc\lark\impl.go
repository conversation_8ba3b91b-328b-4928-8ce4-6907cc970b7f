// Code generated by lark_sdk_gen. DO NOT EDIT.
/**
 * Copyright 2022 chyroc
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package lark

import (
	"time"
)

// Lark client struct
type Lark struct {
	appID               string
	appSecret           string
	encryptKey          string
	verificationToken   string
	helpdeskID          string
	helpdeskToken       string
	timeout             time.Duration
	isISV               bool
	tenantKey           string
	customBotWebHookURL string
	customBotSecret     string
	openBaseURL         string
	wwwBaseURL          string

	httpClient   HttpClient
	logger       Logger
	logLevel     LogLevel
	store        Store
	mock         *Mock
	eventHandler *eventHandler

	// service
	Auth          *AuthService
	Contact       *ContactService
	Message       *MessageService
	Chat          *ChatService
	Bot           *BotService
	Calendar      *CalendarService
	Drive         *DriveService
	Bitable       *BitableService
	MeetingRoom   *MeetingRoomService
	Jssdk         *JssdkService
	VC            *VCService
	Application   *ApplicationService
	Mail          *MailService
	Approval      *ApprovalService
	Helpdesk      *HelpdeskService
	Admin         *AdminService
	HumanAuth     *HumanAuthService
	AI            *AIService
	Attendance    *AttendanceService
	File          *FileService
	OKR           *OKRService
	EHR           *EHRService
	Tenant        *TenantService
	Search        *SearchService
	Hire          *HireService
	Task          *TaskService
	ACS           *ACSService
	Baike         *BaikeService
	Passport      *PassportService
	Event         *EventService
	EventCallback *EventCallbackService
	AppLink       *AppLinkService
}

func (r *Lark) initService() {
	r.Auth = &AuthService{cli: r}
	r.Contact = &ContactService{cli: r}
	r.Message = &MessageService{cli: r}
	r.Chat = &ChatService{cli: r}
	r.Bot = &BotService{cli: r}
	r.Calendar = &CalendarService{cli: r}
	r.Drive = &DriveService{cli: r}
	r.Bitable = &BitableService{cli: r}
	r.MeetingRoom = &MeetingRoomService{cli: r}
	r.Jssdk = &JssdkService{cli: r}
	r.VC = &VCService{cli: r}
	r.Application = &ApplicationService{cli: r}
	r.Mail = &MailService{cli: r}
	r.Approval = &ApprovalService{cli: r}
	r.Helpdesk = &HelpdeskService{cli: r}
	r.Admin = &AdminService{cli: r}
	r.HumanAuth = &HumanAuthService{cli: r}
	r.AI = &AIService{cli: r}
	r.Attendance = &AttendanceService{cli: r}
	r.File = &FileService{cli: r}
	r.OKR = &OKRService{cli: r}
	r.EHR = &EHRService{cli: r}
	r.Tenant = &TenantService{cli: r}
	r.Search = &SearchService{cli: r}
	r.Hire = &HireService{cli: r}
	r.Task = &TaskService{cli: r}
	r.ACS = &ACSService{cli: r}
	r.Baike = &BaikeService{cli: r}
	r.Passport = &PassportService{cli: r}
	r.Event = &EventService{cli: r}
	r.EventCallback = &EventCallbackService{cli: r}
	r.AppLink = &AppLinkService{cli: r}

}

func (r *Lark) clone(tenantKey string) *Lark {
	r2 := &Lark{
		appID:               r.appID,
		appSecret:           r.appSecret,
		encryptKey:          r.encryptKey,
		verificationToken:   r.verificationToken,
		helpdeskID:          r.helpdeskID,
		helpdeskToken:       r.helpdeskToken,
		timeout:             r.timeout,
		isISV:               r.isISV,
		tenantKey:           tenantKey,
		customBotWebHookURL: r.customBotWebHookURL,
		customBotSecret:     r.customBotSecret,
		openBaseURL:         r.openBaseURL,
		wwwBaseURL:          r.wwwBaseURL,
		httpClient:          r.httpClient,
		logger:              r.logger,
		logLevel:            r.logLevel,
		store:               r.store,
		mock:                r.mock,
		eventHandler:        r.eventHandler,
	}
	r2.initService()
	return r2
}

type AuthService struct{ cli *Lark }
type ContactService struct{ cli *Lark }
type MessageService struct{ cli *Lark }
type ChatService struct{ cli *Lark }
type BotService struct{ cli *Lark }
type CalendarService struct{ cli *Lark }
type DriveService struct{ cli *Lark }
type BitableService struct{ cli *Lark }
type MeetingRoomService struct{ cli *Lark }
type JssdkService struct{ cli *Lark }
type VCService struct{ cli *Lark }
type ApplicationService struct{ cli *Lark }
type MailService struct{ cli *Lark }
type ApprovalService struct{ cli *Lark }
type HelpdeskService struct{ cli *Lark }
type AdminService struct{ cli *Lark }
type HumanAuthService struct{ cli *Lark }
type AIService struct{ cli *Lark }
type AttendanceService struct{ cli *Lark }
type FileService struct{ cli *Lark }
type OKRService struct{ cli *Lark }
type EHRService struct{ cli *Lark }
type TenantService struct{ cli *Lark }
type SearchService struct{ cli *Lark }
type HireService struct{ cli *Lark }
type TaskService struct{ cli *Lark }
type ACSService struct{ cli *Lark }
type BaikeService struct{ cli *Lark }
type PassportService struct{ cli *Lark }
type EventService struct{ cli *Lark }
type EventCallbackService struct{ cli *Lark }
type AppLinkService struct{ cli *Lark }
