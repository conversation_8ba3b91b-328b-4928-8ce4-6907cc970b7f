dn: ou=people,dc=zelos,dc=com
ou: people
description: 用户根目录
objectClass: organizationalUnit

dn: ou=dingtalkroot,dc=zelos,dc=com
ou: dingtalkroot
description: 钉钉根部门
objectClass: top
objectClass: organizationalUnit

dn: ou=wecomroot,dc=zelos,dc=com
ou: wecomroot
description: 企业微信根部门
objectClass: top
objectClass: organizationalUnit

dn: ou=feishuroot,dc=zelos,dc=com
ou: feishuroot
description: 飞书根部门
objectClass: top
objectClass: organizationalUnit

dn: cn=group,dc=zelos,dc=com
cn: group
description: 默认分组
objectClass: top
objectClass: groupOfUniqueNames
