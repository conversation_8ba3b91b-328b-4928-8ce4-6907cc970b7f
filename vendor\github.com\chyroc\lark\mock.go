// Code generated by lark_sdk_gen. DO NOT EDIT.
/**
 * Copyright 2022 chyroc
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package lark

import (
	"context"
)

// Mock mock struct
type Mock struct {
	mockRawRequest                                             func(ctx context.Context, req *RawRequestReq, resp interface{}) (response *Response, err error)
	mockGetTenantAccessToken                                   func(ctx context.Context) (*TokenExpire, *Response, error)
	mockGetAppAccessToken                                      func(ctx context.Context) (*TokenExpire, *Response, error)
	mockAuthResendAppTicket                                    func(ctx context.Context, request *ResendAppTicketReq, options ...MethodOptionFunc) (*ResendAppTicketResp, *Response, error)
	mockAuthGetAccessToken                                     func(ctx context.Context, request *GetAccessTokenReq, options ...MethodOptionFunc) (*GetAccessTokenResp, *Response, error)
	mockAuthRefreshAccessToken                                 func(ctx context.Context, request *RefreshAccessTokenReq, options ...MethodOptionFunc) (*RefreshAccessTokenResp, *Response, error)
	mockAuthGetUserInfo                                        func(ctx context.Context, request *GetUserInfoReq, options ...MethodOptionFunc) (*GetUserInfoResp, *Response, error)
	mockContactSearchUserOld                                   func(ctx context.Context, request *SearchUserOldReq, options ...MethodOptionFunc) (*SearchUserOldResp, *Response, error)
	mockContactCreateUser                                      func(ctx context.Context, request *CreateUserReq, options ...MethodOptionFunc) (*CreateUserResp, *Response, error)
	mockContactDeleteUser                                      func(ctx context.Context, request *DeleteUserReq, options ...MethodOptionFunc) (*DeleteUserResp, *Response, error)
	mockContactGetUser                                         func(ctx context.Context, request *GetUserReq, options ...MethodOptionFunc) (*GetUserResp, *Response, error)
	mockContactGetUserList                                     func(ctx context.Context, request *GetUserListReq, options ...MethodOptionFunc) (*GetUserListResp, *Response, error)
	mockContactGetUserListOld                                  func(ctx context.Context, request *GetUserListOldReq, options ...MethodOptionFunc) (*GetUserListOldResp, *Response, error)
	mockContactBatchGetUser                                    func(ctx context.Context, request *BatchGetUserReq, options ...MethodOptionFunc) (*BatchGetUserResp, *Response, error)
	mockContactUpdateUserPatch                                 func(ctx context.Context, request *UpdateUserPatchReq, options ...MethodOptionFunc) (*UpdateUserPatchResp, *Response, error)
	mockContactUpdateUser                                      func(ctx context.Context, request *UpdateUserReq, options ...MethodOptionFunc) (*UpdateUserResp, *Response, error)
	mockContactBatchGetUserByID                                func(ctx context.Context, request *BatchGetUserByIDReq, options ...MethodOptionFunc) (*BatchGetUserByIDResp, *Response, error)
	mockContactBatchGetUserByIDOld                             func(ctx context.Context, request *BatchGetUserByIDOldReq, options ...MethodOptionFunc) (*BatchGetUserByIDOldResp, *Response, error)
	mockContactCreateDepartment                                func(ctx context.Context, request *CreateDepartmentReq, options ...MethodOptionFunc) (*CreateDepartmentResp, *Response, error)
	mockContactGetDepartment                                   func(ctx context.Context, request *GetDepartmentReq, options ...MethodOptionFunc) (*GetDepartmentResp, *Response, error)
	mockContactGetDepartmentList                               func(ctx context.Context, request *GetDepartmentListReq, options ...MethodOptionFunc) (*GetDepartmentListResp, *Response, error)
	mockContactGetDepartmentListOld                            func(ctx context.Context, request *GetDepartmentListOldReq, options ...MethodOptionFunc) (*GetDepartmentListOldResp, *Response, error)
	mockContactGetParentDepartment                             func(ctx context.Context, request *GetParentDepartmentReq, options ...MethodOptionFunc) (*GetParentDepartmentResp, *Response, error)
	mockContactSearchDepartment                                func(ctx context.Context, request *SearchDepartmentReq, options ...MethodOptionFunc) (*SearchDepartmentResp, *Response, error)
	mockContactUpdateDepartmentPatch                           func(ctx context.Context, request *UpdateDepartmentPatchReq, options ...MethodOptionFunc) (*UpdateDepartmentPatchResp, *Response, error)
	mockContactUpdateDepartment                                func(ctx context.Context, request *UpdateDepartmentReq, options ...MethodOptionFunc) (*UpdateDepartmentResp, *Response, error)
	mockContactDeleteDepartment                                func(ctx context.Context, request *DeleteDepartmentReq, options ...MethodOptionFunc) (*DeleteDepartmentResp, *Response, error)
	mockContactUnbindDepartmentChat                            func(ctx context.Context, request *UnbindDepartmentChatReq, options ...MethodOptionFunc) (*UnbindDepartmentChatResp, *Response, error)
	mockContactCreateContactGroup                              func(ctx context.Context, request *CreateContactGroupReq, options ...MethodOptionFunc) (*CreateContactGroupResp, *Response, error)
	mockContactUpdateContactGroup                              func(ctx context.Context, request *UpdateContactGroupReq, options ...MethodOptionFunc) (*UpdateContactGroupResp, *Response, error)
	mockContactDeleteContactGroup                              func(ctx context.Context, request *DeleteContactGroupReq, options ...MethodOptionFunc) (*DeleteContactGroupResp, *Response, error)
	mockContactGetContactGroup                                 func(ctx context.Context, request *GetContactGroupReq, options ...MethodOptionFunc) (*GetContactGroupResp, *Response, error)
	mockContactGetContactGroupList                             func(ctx context.Context, request *GetContactGroupListReq, options ...MethodOptionFunc) (*GetContactGroupListResp, *Response, error)
	mockContactAddContactGroupMember                           func(ctx context.Context, request *AddContactGroupMemberReq, options ...MethodOptionFunc) (*AddContactGroupMemberResp, *Response, error)
	mockContactDeleteContactGroupMember                        func(ctx context.Context, request *DeleteContactGroupMemberReq, options ...MethodOptionFunc) (*DeleteContactGroupMemberResp, *Response, error)
	mockContactGetContactGroupMember                           func(ctx context.Context, request *GetContactGroupMemberReq, options ...MethodOptionFunc) (*GetContactGroupMemberResp, *Response, error)
	mockContactGetEmployeeTypeEnumList                         func(ctx context.Context, request *GetEmployeeTypeEnumListReq, options ...MethodOptionFunc) (*GetEmployeeTypeEnumListResp, *Response, error)
	mockContactUpdateEmployeeTypeEnumPatch                     func(ctx context.Context, request *UpdateEmployeeTypeEnumPatchReq, options ...MethodOptionFunc) (*UpdateEmployeeTypeEnumPatchResp, *Response, error)
	mockContactDeleteEmployeeTypeEnum                          func(ctx context.Context, request *DeleteEmployeeTypeEnumReq, options ...MethodOptionFunc) (*DeleteEmployeeTypeEnumResp, *Response, error)
	mockContactCreateEmployeeTypeEnum                          func(ctx context.Context, request *CreateEmployeeTypeEnumReq, options ...MethodOptionFunc) (*CreateEmployeeTypeEnumResp, *Response, error)
	mockContactGetContactCustomAttrList                        func(ctx context.Context, request *GetContactCustomAttrListReq, options ...MethodOptionFunc) (*GetContactCustomAttrListResp, *Response, error)
	mockContactCreateContactUnit                               func(ctx context.Context, request *CreateContactUnitReq, options ...MethodOptionFunc) (*CreateContactUnitResp, *Response, error)
	mockContactUpdateContactUnit                               func(ctx context.Context, request *UpdateContactUnitReq, options ...MethodOptionFunc) (*UpdateContactUnitResp, *Response, error)
	mockContactDeleteContactUnit                               func(ctx context.Context, request *DeleteContactUnitReq, options ...MethodOptionFunc) (*DeleteContactUnitResp, *Response, error)
	mockContactGetContactUnit                                  func(ctx context.Context, request *GetContactUnitReq, options ...MethodOptionFunc) (*GetContactUnitResp, *Response, error)
	mockContactGetContactUnitList                              func(ctx context.Context, request *GetContactUnitListReq, options ...MethodOptionFunc) (*GetContactUnitListResp, *Response, error)
	mockContactBindContactUnitDepartment                       func(ctx context.Context, request *BindContactUnitDepartmentReq, options ...MethodOptionFunc) (*BindContactUnitDepartmentResp, *Response, error)
	mockContactUnbindContactUnitDepartment                     func(ctx context.Context, request *UnbindContactUnitDepartmentReq, options ...MethodOptionFunc) (*UnbindContactUnitDepartmentResp, *Response, error)
	mockContactGetContactUnitDepartmentList                    func(ctx context.Context, request *GetContactUnitDepartmentListReq, options ...MethodOptionFunc) (*GetContactUnitDepartmentListResp, *Response, error)
	mockContactGetContactScopeList                             func(ctx context.Context, request *GetContactScopeListReq, options ...MethodOptionFunc) (*GetContactScopeListResp, *Response, error)
	mockMessageSendEphemeralMessage                            func(ctx context.Context, request *SendEphemeralMessageReq, options ...MethodOptionFunc) (*SendEphemeralMessageResp, *Response, error)
	mockMessageSendUrgentAppMessage                            func(ctx context.Context, request *SendUrgentAppMessageReq, options ...MethodOptionFunc) (*SendUrgentAppMessageResp, *Response, error)
	mockMessageSendUrgentSmsMessage                            func(ctx context.Context, request *SendUrgentSmsMessageReq, options ...MethodOptionFunc) (*SendUrgentSmsMessageResp, *Response, error)
	mockMessageSendUrgentPhoneMessage                          func(ctx context.Context, request *SendUrgentPhoneMessageReq, options ...MethodOptionFunc) (*SendUrgentPhoneMessageResp, *Response, error)
	mockMessageSendRawMessage                                  func(ctx context.Context, request *SendRawMessageReq, options ...MethodOptionFunc) (*SendRawMessageResp, *Response, error)
	mockMessageSendRawMessageOld                               func(ctx context.Context, request *SendRawMessageOldReq, options ...MethodOptionFunc) (*SendRawMessageOldResp, *Response, error)
	mockMessageBatchSendOldRawMessage                          func(ctx context.Context, request *BatchSendOldRawMessageReq, options ...MethodOptionFunc) (*BatchSendOldRawMessageResp, *Response, error)
	mockMessageReplyRawMessage                                 func(ctx context.Context, request *ReplyRawMessageReq, options ...MethodOptionFunc) (*ReplyRawMessageResp, *Response, error)
	mockMessageDeleteMessage                                   func(ctx context.Context, request *DeleteMessageReq, options ...MethodOptionFunc) (*DeleteMessageResp, *Response, error)
	mockMessageBatchDeleteMessage                              func(ctx context.Context, request *BatchDeleteMessageReq, options ...MethodOptionFunc) (*BatchDeleteMessageResp, *Response, error)
	mockMessageUpdateMessage                                   func(ctx context.Context, request *UpdateMessageReq, options ...MethodOptionFunc) (*UpdateMessageResp, *Response, error)
	mockMessageUpdateMessageDelay                              func(ctx context.Context, request *UpdateMessageDelayReq, options ...MethodOptionFunc) (*UpdateMessageDelayResp, *Response, error)
	mockMessageGetMessageReadUserList                          func(ctx context.Context, request *GetMessageReadUserListReq, options ...MethodOptionFunc) (*GetMessageReadUserListResp, *Response, error)
	mockMessageGetBatchSentMessageReadUser                     func(ctx context.Context, request *GetBatchSentMessageReadUserReq, options ...MethodOptionFunc) (*GetBatchSentMessageReadUserResp, *Response, error)
	mockMessageGetBatchSentMessageProgress                     func(ctx context.Context, request *GetBatchSentMessageProgressReq, options ...MethodOptionFunc) (*GetBatchSentMessageProgressResp, *Response, error)
	mockMessageGetMessageList                                  func(ctx context.Context, request *GetMessageListReq, options ...MethodOptionFunc) (*GetMessageListResp, *Response, error)
	mockMessageGetMessageFile                                  func(ctx context.Context, request *GetMessageFileReq, options ...MethodOptionFunc) (*GetMessageFileResp, *Response, error)
	mockMessageGetMessage                                      func(ctx context.Context, request *GetMessageReq, options ...MethodOptionFunc) (*GetMessageResp, *Response, error)
	mockMessageDeleteEphemeralMessage                          func(ctx context.Context, request *DeleteEphemeralMessageReq, options ...MethodOptionFunc) (*DeleteEphemeralMessageResp, *Response, error)
	mockMessageCreateMessageReaction                           func(ctx context.Context, request *CreateMessageReactionReq, options ...MethodOptionFunc) (*CreateMessageReactionResp, *Response, error)
	mockMessageGetMessageReactionList                          func(ctx context.Context, request *GetMessageReactionListReq, options ...MethodOptionFunc) (*GetMessageReactionListResp, *Response, error)
	mockMessageDeleteMessageReaction                           func(ctx context.Context, request *DeleteMessageReactionReq, options ...MethodOptionFunc) (*DeleteMessageReactionResp, *Response, error)
	mockChatCreateChat                                         func(ctx context.Context, request *CreateChatReq, options ...MethodOptionFunc) (*CreateChatResp, *Response, error)
	mockChatGetChat                                            func(ctx context.Context, request *GetChatReq, options ...MethodOptionFunc) (*GetChatResp, *Response, error)
	mockChatGetChatOld                                         func(ctx context.Context, request *GetChatOldReq, options ...MethodOptionFunc) (*GetChatOldResp, *Response, error)
	mockChatUpdateChat                                         func(ctx context.Context, request *UpdateChatReq, options ...MethodOptionFunc) (*UpdateChatResp, *Response, error)
	mockChatDeleteChat                                         func(ctx context.Context, request *DeleteChatReq, options ...MethodOptionFunc) (*DeleteChatResp, *Response, error)
	mockChatGetChatListOfSelf                                  func(ctx context.Context, request *GetChatListOfSelfReq, options ...MethodOptionFunc) (*GetChatListOfSelfResp, *Response, error)
	mockChatSearchChat                                         func(ctx context.Context, request *SearchChatReq, options ...MethodOptionFunc) (*SearchChatResp, *Response, error)
	mockChatGetChatMemberList                                  func(ctx context.Context, request *GetChatMemberListReq, options ...MethodOptionFunc) (*GetChatMemberListResp, *Response, error)
	mockChatIsInChat                                           func(ctx context.Context, request *IsInChatReq, options ...MethodOptionFunc) (*IsInChatResp, *Response, error)
	mockChatCreateChatManager                                  func(ctx context.Context, request *CreateChatManagerReq, options ...MethodOptionFunc) (*CreateChatManagerResp, *Response, error)
	mockChatDeleteChatManager                                  func(ctx context.Context, request *DeleteChatManagerReq, options ...MethodOptionFunc) (*DeleteChatManagerResp, *Response, error)
	mockChatAddChatMember                                      func(ctx context.Context, request *AddChatMemberReq, options ...MethodOptionFunc) (*AddChatMemberResp, *Response, error)
	mockChatDeleteChatMember                                   func(ctx context.Context, request *DeleteChatMemberReq, options ...MethodOptionFunc) (*DeleteChatMemberResp, *Response, error)
	mockChatJoinChat                                           func(ctx context.Context, request *JoinChatReq, options ...MethodOptionFunc) (*JoinChatResp, *Response, error)
	mockChatGetChatAnnouncement                                func(ctx context.Context, request *GetChatAnnouncementReq, options ...MethodOptionFunc) (*GetChatAnnouncementResp, *Response, error)
	mockChatUpdateChatAnnouncement                             func(ctx context.Context, request *UpdateChatAnnouncementReq, options ...MethodOptionFunc) (*UpdateChatAnnouncementResp, *Response, error)
	mockChatGetChatModeration                                  func(ctx context.Context, request *GetChatModerationReq, options ...MethodOptionFunc) (*GetChatModerationResp, *Response, error)
	mockChatUpdateChatModeration                               func(ctx context.Context, request *UpdateChatModerationReq, options ...MethodOptionFunc) (*UpdateChatModerationResp, *Response, error)
	mockChatUpdateChatTopNotice                                func(ctx context.Context, request *UpdateChatTopNoticeReq, options ...MethodOptionFunc) (*UpdateChatTopNoticeResp, *Response, error)
	mockChatDeleteChatTopNotice                                func(ctx context.Context, request *DeleteChatTopNoticeReq, options ...MethodOptionFunc) (*DeleteChatTopNoticeResp, *Response, error)
	mockBotGetBotInfo                                          func(ctx context.Context, request *GetBotInfoReq, options ...MethodOptionFunc) (*GetBotInfoResp, *Response, error)
	mockBotAddBotToChat                                        func(ctx context.Context, request *AddBotToChatReq, options ...MethodOptionFunc) (*AddBotToChatResp, *Response, error)
	mockCalendarCreateCalendarACL                              func(ctx context.Context, request *CreateCalendarACLReq, options ...MethodOptionFunc) (*CreateCalendarACLResp, *Response, error)
	mockCalendarDeleteCalendarACL                              func(ctx context.Context, request *DeleteCalendarACLReq, options ...MethodOptionFunc) (*DeleteCalendarACLResp, *Response, error)
	mockCalendarGetCalendarACLList                             func(ctx context.Context, request *GetCalendarACLListReq, options ...MethodOptionFunc) (*GetCalendarACLListResp, *Response, error)
	mockCalendarSubscribeCalendarACL                           func(ctx context.Context, request *SubscribeCalendarACLReq, options ...MethodOptionFunc) (*SubscribeCalendarACLResp, *Response, error)
	mockCalendarGetPrimaryCalendar                             func(ctx context.Context, request *GetPrimaryCalendarReq, options ...MethodOptionFunc) (*GetPrimaryCalendarResp, *Response, error)
	mockCalendarCreateCalendar                                 func(ctx context.Context, request *CreateCalendarReq, options ...MethodOptionFunc) (*CreateCalendarResp, *Response, error)
	mockCalendarDeleteCalendar                                 func(ctx context.Context, request *DeleteCalendarReq, options ...MethodOptionFunc) (*DeleteCalendarResp, *Response, error)
	mockCalendarGetCalendar                                    func(ctx context.Context, request *GetCalendarReq, options ...MethodOptionFunc) (*GetCalendarResp, *Response, error)
	mockCalendarGetCalendarList                                func(ctx context.Context, request *GetCalendarListReq, options ...MethodOptionFunc) (*GetCalendarListResp, *Response, error)
	mockCalendarUpdateCalendar                                 func(ctx context.Context, request *UpdateCalendarReq, options ...MethodOptionFunc) (*UpdateCalendarResp, *Response, error)
	mockCalendarSearchCalendar                                 func(ctx context.Context, request *SearchCalendarReq, options ...MethodOptionFunc) (*SearchCalendarResp, *Response, error)
	mockCalendarSubscribeCalendar                              func(ctx context.Context, request *SubscribeCalendarReq, options ...MethodOptionFunc) (*SubscribeCalendarResp, *Response, error)
	mockCalendarUnsubscribeCalendar                            func(ctx context.Context, request *UnsubscribeCalendarReq, options ...MethodOptionFunc) (*UnsubscribeCalendarResp, *Response, error)
	mockCalendarSubscribeCalendarChangeEvent                   func(ctx context.Context, request *SubscribeCalendarChangeEventReq, options ...MethodOptionFunc) (*SubscribeCalendarChangeEventResp, *Response, error)
	mockCalendarCreateCalendarEvent                            func(ctx context.Context, request *CreateCalendarEventReq, options ...MethodOptionFunc) (*CreateCalendarEventResp, *Response, error)
	mockCalendarDeleteCalendarEvent                            func(ctx context.Context, request *DeleteCalendarEventReq, options ...MethodOptionFunc) (*DeleteCalendarEventResp, *Response, error)
	mockCalendarGetCalendarEvent                               func(ctx context.Context, request *GetCalendarEventReq, options ...MethodOptionFunc) (*GetCalendarEventResp, *Response, error)
	mockCalendarGetCalendarEventList                           func(ctx context.Context, request *GetCalendarEventListReq, options ...MethodOptionFunc) (*GetCalendarEventListResp, *Response, error)
	mockCalendarUpdateCalendarEvent                            func(ctx context.Context, request *UpdateCalendarEventReq, options ...MethodOptionFunc) (*UpdateCalendarEventResp, *Response, error)
	mockCalendarSearchCalendarEvent                            func(ctx context.Context, request *SearchCalendarEventReq, options ...MethodOptionFunc) (*SearchCalendarEventResp, *Response, error)
	mockCalendarSubscribeCalendarEvent                         func(ctx context.Context, request *SubscribeCalendarEventReq, options ...MethodOptionFunc) (*SubscribeCalendarEventResp, *Response, error)
	mockCalendarCreateCalendarEventAttendee                    func(ctx context.Context, request *CreateCalendarEventAttendeeReq, options ...MethodOptionFunc) (*CreateCalendarEventAttendeeResp, *Response, error)
	mockCalendarGetCalendarEventAttendeeList                   func(ctx context.Context, request *GetCalendarEventAttendeeListReq, options ...MethodOptionFunc) (*GetCalendarEventAttendeeListResp, *Response, error)
	mockCalendarDeleteCalendarEventAttendee                    func(ctx context.Context, request *DeleteCalendarEventAttendeeReq, options ...MethodOptionFunc) (*DeleteCalendarEventAttendeeResp, *Response, error)
	mockCalendarGetCalendarEventAttendeeChatMemberList         func(ctx context.Context, request *GetCalendarEventAttendeeChatMemberListReq, options ...MethodOptionFunc) (*GetCalendarEventAttendeeChatMemberListResp, *Response, error)
	mockCalendarGetCalendarFreeBusyList                        func(ctx context.Context, request *GetCalendarFreeBusyListReq, options ...MethodOptionFunc) (*GetCalendarFreeBusyListResp, *Response, error)
	mockCalendarCreateCalendarTimeoffEvent                     func(ctx context.Context, request *CreateCalendarTimeoffEventReq, options ...MethodOptionFunc) (*CreateCalendarTimeoffEventResp, *Response, error)
	mockCalendarDeleteCalendarTimeoffEvent                     func(ctx context.Context, request *DeleteCalendarTimeoffEventReq, options ...MethodOptionFunc) (*DeleteCalendarTimeoffEventResp, *Response, error)
	mockCalendarGenerateCaldavConf                             func(ctx context.Context, request *GenerateCaldavConfReq, options ...MethodOptionFunc) (*GenerateCaldavConfResp, *Response, error)
	mockCalendarCreateCalendarExchangeBinding                  func(ctx context.Context, request *CreateCalendarExchangeBindingReq, options ...MethodOptionFunc) (*CreateCalendarExchangeBindingResp, *Response, error)
	mockCalendarGetCalendarExchangeBinding                     func(ctx context.Context, request *GetCalendarExchangeBindingReq, options ...MethodOptionFunc) (*GetCalendarExchangeBindingResp, *Response, error)
	mockCalendarDeleteCalendarExchangeBinding                  func(ctx context.Context, request *DeleteCalendarExchangeBindingReq, options ...MethodOptionFunc) (*DeleteCalendarExchangeBindingResp, *Response, error)
	mockDriveGetDriveDocxDocument                              func(ctx context.Context, request *GetDriveDocxDocumentReq, options ...MethodOptionFunc) (*GetDriveDocxDocumentResp, *Response, error)
	mockDriveGetDriveDocxDocumentRawContent                    func(ctx context.Context, request *GetDriveDocxDocumentRawContentReq, options ...MethodOptionFunc) (*GetDriveDocxDocumentRawContentResp, *Response, error)
	mockDriveSubscribeDriveFile                                func(ctx context.Context, request *SubscribeDriveFileReq, options ...MethodOptionFunc) (*SubscribeDriveFileResp, *Response, error)
	mockDriveSearchDriveFile                                   func(ctx context.Context, request *SearchDriveFileReq, options ...MethodOptionFunc) (*SearchDriveFileResp, *Response, error)
	mockDriveGetDriveFileMeta                                  func(ctx context.Context, request *GetDriveFileMetaReq, options ...MethodOptionFunc) (*GetDriveFileMetaResp, *Response, error)
	mockDriveCreateDriveFile                                   func(ctx context.Context, request *CreateDriveFileReq, options ...MethodOptionFunc) (*CreateDriveFileResp, *Response, error)
	mockDriveDeleteDriveFile                                   func(ctx context.Context, request *DeleteDriveFileReq, options ...MethodOptionFunc) (*DeleteDriveFileResp, *Response, error)
	mockDriveDeleteDriveSheetFile                              func(ctx context.Context, request *DeleteDriveSheetFileReq, options ...MethodOptionFunc) (*DeleteDriveSheetFileResp, *Response, error)
	mockDriveGetDriveFolderMeta                                func(ctx context.Context, request *GetDriveFolderMetaReq, options ...MethodOptionFunc) (*GetDriveFolderMetaResp, *Response, error)
	mockDriveGetDriveRootFolderMeta                            func(ctx context.Context, request *GetDriveRootFolderMetaReq, options ...MethodOptionFunc) (*GetDriveRootFolderMetaResp, *Response, error)
	mockDriveGetDriveFolderChildren                            func(ctx context.Context, request *GetDriveFolderChildrenReq, options ...MethodOptionFunc) (*GetDriveFolderChildrenResp, *Response, error)
	mockDriveGetDriveFileStatistics                            func(ctx context.Context, request *GetDriveFileStatisticsReq, options ...MethodOptionFunc) (*GetDriveFileStatisticsResp, *Response, error)
	mockDriveGetDriveFileTask                                  func(ctx context.Context, request *GetDriveFileTaskReq, options ...MethodOptionFunc) (*GetDriveFileTaskResp, *Response, error)
	mockDriveCreateDriveExportTask                             func(ctx context.Context, request *CreateDriveExportTaskReq, options ...MethodOptionFunc) (*CreateDriveExportTaskResp, *Response, error)
	mockDriveGetDriveExportTask                                func(ctx context.Context, request *GetDriveExportTaskReq, options ...MethodOptionFunc) (*GetDriveExportTaskResp, *Response, error)
	mockDriveDownloadDriveExportTask                           func(ctx context.Context, request *DownloadDriveExportTaskReq, options ...MethodOptionFunc) (*DownloadDriveExportTaskResp, *Response, error)
	mockDriveDownloadDriveFile                                 func(ctx context.Context, request *DownloadDriveFileReq, options ...MethodOptionFunc) (*DownloadDriveFileResp, *Response, error)
	mockDriveCopyDriveFile                                     func(ctx context.Context, request *CopyDriveFileReq, options ...MethodOptionFunc) (*CopyDriveFileResp, *Response, error)
	mockDriveCreateDriveFolder                                 func(ctx context.Context, request *CreateDriveFolderReq, options ...MethodOptionFunc) (*CreateDriveFolderResp, *Response, error)
	mockDriveMoveDriveFile                                     func(ctx context.Context, request *MoveDriveFileReq, options ...MethodOptionFunc) (*MoveDriveFileResp, *Response, error)
	mockDriveUploadDriveFile                                   func(ctx context.Context, request *UploadDriveFileReq, options ...MethodOptionFunc) (*UploadDriveFileResp, *Response, error)
	mockDrivePrepareUploadDriveFile                            func(ctx context.Context, request *PrepareUploadDriveFileReq, options ...MethodOptionFunc) (*PrepareUploadDriveFileResp, *Response, error)
	mockDrivePartUploadDriveFile                               func(ctx context.Context, request *PartUploadDriveFileReq, options ...MethodOptionFunc) (*PartUploadDriveFileResp, *Response, error)
	mockDriveFinishUploadDriveFile                             func(ctx context.Context, request *FinishUploadDriveFileReq, options ...MethodOptionFunc) (*FinishUploadDriveFileResp, *Response, error)
	mockDriveDownloadDriveMedia                                func(ctx context.Context, request *DownloadDriveMediaReq, options ...MethodOptionFunc) (*DownloadDriveMediaResp, *Response, error)
	mockDriveUploadDriveMedia                                  func(ctx context.Context, request *UploadDriveMediaReq, options ...MethodOptionFunc) (*UploadDriveMediaResp, *Response, error)
	mockDrivePrepareUploadDriveMedia                           func(ctx context.Context, request *PrepareUploadDriveMediaReq, options ...MethodOptionFunc) (*PrepareUploadDriveMediaResp, *Response, error)
	mockDrivePartUploadDriveMedia                              func(ctx context.Context, request *PartUploadDriveMediaReq, options ...MethodOptionFunc) (*PartUploadDriveMediaResp, *Response, error)
	mockDriveFinishUploadDriveMedia                            func(ctx context.Context, request *FinishUploadDriveMediaReq, options ...MethodOptionFunc) (*FinishUploadDriveMediaResp, *Response, error)
	mockDriveCreateDriveMemberPermissionOld                    func(ctx context.Context, request *CreateDriveMemberPermissionOldReq, options ...MethodOptionFunc) (*CreateDriveMemberPermissionOldResp, *Response, error)
	mockDriveTransferDriveMemberPermission                     func(ctx context.Context, request *TransferDriveMemberPermissionReq, options ...MethodOptionFunc) (*TransferDriveMemberPermissionResp, *Response, error)
	mockDriveGetDriveMemberPermissionList                      func(ctx context.Context, request *GetDriveMemberPermissionListReq, options ...MethodOptionFunc) (*GetDriveMemberPermissionListResp, *Response, error)
	mockDriveCreateDriveMemberPermission                       func(ctx context.Context, request *CreateDriveMemberPermissionReq, options ...MethodOptionFunc) (*CreateDriveMemberPermissionResp, *Response, error)
	mockDriveDeleteDriveMemberPermission                       func(ctx context.Context, request *DeleteDriveMemberPermissionReq, options ...MethodOptionFunc) (*DeleteDriveMemberPermissionResp, *Response, error)
	mockDriveDeleteDriveMemberPermissionOld                    func(ctx context.Context, request *DeleteDriveMemberPermissionOldReq, options ...MethodOptionFunc) (*DeleteDriveMemberPermissionOldResp, *Response, error)
	mockDriveUpdateDriveMemberPermissionOld                    func(ctx context.Context, request *UpdateDriveMemberPermissionOldReq, options ...MethodOptionFunc) (*UpdateDriveMemberPermissionOldResp, *Response, error)
	mockDriveUpdateDriveMemberPermission                       func(ctx context.Context, request *UpdateDriveMemberPermissionReq, options ...MethodOptionFunc) (*UpdateDriveMemberPermissionResp, *Response, error)
	mockDriveCheckDriveMemberPermission                        func(ctx context.Context, request *CheckDriveMemberPermissionReq, options ...MethodOptionFunc) (*CheckDriveMemberPermissionResp, *Response, error)
	mockDriveGetDrivePublicPermission                          func(ctx context.Context, request *GetDrivePublicPermissionReq, options ...MethodOptionFunc) (*GetDrivePublicPermissionResp, *Response, error)
	mockDriveUpdateDrivePublicPermission                       func(ctx context.Context, request *UpdateDrivePublicPermissionReq, options ...MethodOptionFunc) (*UpdateDrivePublicPermissionResp, *Response, error)
	mockDriveBatchGetDriveMediaTmpDownloadURL                  func(ctx context.Context, request *BatchGetDriveMediaTmpDownloadURLReq, options ...MethodOptionFunc) (*BatchGetDriveMediaTmpDownloadURLResp, *Response, error)
	mockDriveGetDriveCommentList                               func(ctx context.Context, request *GetDriveCommentListReq, options ...MethodOptionFunc) (*GetDriveCommentListResp, *Response, error)
	mockDriveGetDriveComment                                   func(ctx context.Context, request *GetDriveCommentReq, options ...MethodOptionFunc) (*GetDriveCommentResp, *Response, error)
	mockDriveCreateDriveComment                                func(ctx context.Context, request *CreateDriveCommentReq, options ...MethodOptionFunc) (*CreateDriveCommentResp, *Response, error)
	mockDriveUpdateDriveComment                                func(ctx context.Context, request *UpdateDriveCommentReq, options ...MethodOptionFunc) (*UpdateDriveCommentResp, *Response, error)
	mockDriveDeleteDriveComment                                func(ctx context.Context, request *DeleteDriveCommentReq, options ...MethodOptionFunc) (*DeleteDriveCommentResp, *Response, error)
	mockDriveUpdateDriveCommentPatch                           func(ctx context.Context, request *UpdateDriveCommentPatchReq, options ...MethodOptionFunc) (*UpdateDriveCommentPatchResp, *Response, error)
	mockDriveCreateDriveFileSubscription                       func(ctx context.Context, request *CreateDriveFileSubscriptionReq, options ...MethodOptionFunc) (*CreateDriveFileSubscriptionResp, *Response, error)
	mockDriveGetDriveFileSubscription                          func(ctx context.Context, request *GetDriveFileSubscriptionReq, options ...MethodOptionFunc) (*GetDriveFileSubscriptionResp, *Response, error)
	mockDriveUpdateDriveFileSubscription                       func(ctx context.Context, request *UpdateDriveFileSubscriptionReq, options ...MethodOptionFunc) (*UpdateDriveFileSubscriptionResp, *Response, error)
	mockDriveCreateDriveDoc                                    func(ctx context.Context, request *CreateDriveDocReq, options ...MethodOptionFunc) (*CreateDriveDocResp, *Response, error)
	mockDriveGetDriveDocContent                                func(ctx context.Context, request *GetDriveDocContentReq, options ...MethodOptionFunc) (*GetDriveDocContentResp, *Response, error)
	mockDriveUpdateDriveDocContent                             func(ctx context.Context, request *UpdateDriveDocContentReq, options ...MethodOptionFunc) (*UpdateDriveDocContentResp, *Response, error)
	mockDriveGetDriveDocRawContent                             func(ctx context.Context, request *GetDriveDocRawContentReq, options ...MethodOptionFunc) (*GetDriveDocRawContentResp, *Response, error)
	mockDriveGetDriveDocMeta                                   func(ctx context.Context, request *GetDriveDocMetaReq, options ...MethodOptionFunc) (*GetDriveDocMetaResp, *Response, error)
	mockDriveCreateSheet                                       func(ctx context.Context, request *CreateSheetReq, options ...MethodOptionFunc) (*CreateSheetResp, *Response, error)
	mockDriveGetSheetMeta                                      func(ctx context.Context, request *GetSheetMetaReq, options ...MethodOptionFunc) (*GetSheetMetaResp, *Response, error)
	mockDriveUpdateSheetProperty                               func(ctx context.Context, request *UpdateSheetPropertyReq, options ...MethodOptionFunc) (*UpdateSheetPropertyResp, *Response, error)
	mockDriveBatchUpdateSheet                                  func(ctx context.Context, request *BatchUpdateSheetReq, options ...MethodOptionFunc) (*BatchUpdateSheetResp, *Response, error)
	mockDriveImportSheet                                       func(ctx context.Context, request *ImportSheetReq, options ...MethodOptionFunc) (*ImportSheetResp, *Response, error)
	mockDriveCreateDriveImportTask                             func(ctx context.Context, request *CreateDriveImportTaskReq, options ...MethodOptionFunc) (*CreateDriveImportTaskResp, *Response, error)
	mockDriveGetDriveImportTask                                func(ctx context.Context, request *GetDriveImportTaskReq, options ...MethodOptionFunc) (*GetDriveImportTaskResp, *Response, error)
	mockDriveMoveSheetDimension                                func(ctx context.Context, request *MoveSheetDimensionReq, options ...MethodOptionFunc) (*MoveSheetDimensionResp, *Response, error)
	mockDrivePrependSheetValue                                 func(ctx context.Context, request *PrependSheetValueReq, options ...MethodOptionFunc) (*PrependSheetValueResp, *Response, error)
	mockDriveAppendSheetValue                                  func(ctx context.Context, request *AppendSheetValueReq, options ...MethodOptionFunc) (*AppendSheetValueResp, *Response, error)
	mockDriveInsertSheetDimensionRange                         func(ctx context.Context, request *InsertSheetDimensionRangeReq, options ...MethodOptionFunc) (*InsertSheetDimensionRangeResp, *Response, error)
	mockDriveAddSheetDimensionRange                            func(ctx context.Context, request *AddSheetDimensionRangeReq, options ...MethodOptionFunc) (*AddSheetDimensionRangeResp, *Response, error)
	mockDriveUpdateSheetDimensionRange                         func(ctx context.Context, request *UpdateSheetDimensionRangeReq, options ...MethodOptionFunc) (*UpdateSheetDimensionRangeResp, *Response, error)
	mockDriveDeleteSheetDimensionRange                         func(ctx context.Context, request *DeleteSheetDimensionRangeReq, options ...MethodOptionFunc) (*DeleteSheetDimensionRangeResp, *Response, error)
	mockDriveGetSheetValue                                     func(ctx context.Context, request *GetSheetValueReq, options ...MethodOptionFunc) (*GetSheetValueResp, *Response, error)
	mockDriveBatchGetSheetValue                                func(ctx context.Context, request *BatchGetSheetValueReq, options ...MethodOptionFunc) (*BatchGetSheetValueResp, *Response, error)
	mockDriveSetSheetValue                                     func(ctx context.Context, request *SetSheetValueReq, options ...MethodOptionFunc) (*SetSheetValueResp, *Response, error)
	mockDriveBatchSetSheetValue                                func(ctx context.Context, request *BatchSetSheetValueReq, options ...MethodOptionFunc) (*BatchSetSheetValueResp, *Response, error)
	mockDriveSetSheetStyle                                     func(ctx context.Context, request *SetSheetStyleReq, options ...MethodOptionFunc) (*SetSheetStyleResp, *Response, error)
	mockDriveBatchSetSheetStyle                                func(ctx context.Context, request *BatchSetSheetStyleReq, options ...MethodOptionFunc) (*BatchSetSheetStyleResp, *Response, error)
	mockDriveMergeSheetCell                                    func(ctx context.Context, request *MergeSheetCellReq, options ...MethodOptionFunc) (*MergeSheetCellResp, *Response, error)
	mockDriveUnmergeSheetCell                                  func(ctx context.Context, request *UnmergeSheetCellReq, options ...MethodOptionFunc) (*UnmergeSheetCellResp, *Response, error)
	mockDriveSetSheetValueImage                                func(ctx context.Context, request *SetSheetValueImageReq, options ...MethodOptionFunc) (*SetSheetValueImageResp, *Response, error)
	mockDriveFindSheet                                         func(ctx context.Context, request *FindSheetReq, options ...MethodOptionFunc) (*FindSheetResp, *Response, error)
	mockDriveReplaceSheet                                      func(ctx context.Context, request *ReplaceSheetReq, options ...MethodOptionFunc) (*ReplaceSheetResp, *Response, error)
	mockDriveCreateSheetConditionFormat                        func(ctx context.Context, request *CreateSheetConditionFormatReq, options ...MethodOptionFunc) (*CreateSheetConditionFormatResp, *Response, error)
	mockDriveGetSheetConditionFormat                           func(ctx context.Context, request *GetSheetConditionFormatReq, options ...MethodOptionFunc) (*GetSheetConditionFormatResp, *Response, error)
	mockDriveUpdateSheetConditionFormat                        func(ctx context.Context, request *UpdateSheetConditionFormatReq, options ...MethodOptionFunc) (*UpdateSheetConditionFormatResp, *Response, error)
	mockDriveDeleteSheetConditionFormat                        func(ctx context.Context, request *DeleteSheetConditionFormatReq, options ...MethodOptionFunc) (*DeleteSheetConditionFormatResp, *Response, error)
	mockDriveCreateSheetProtectedDimension                     func(ctx context.Context, request *CreateSheetProtectedDimensionReq, options ...MethodOptionFunc) (*CreateSheetProtectedDimensionResp, *Response, error)
	mockDriveGetSheetProtectedDimension                        func(ctx context.Context, request *GetSheetProtectedDimensionReq, options ...MethodOptionFunc) (*GetSheetProtectedDimensionResp, *Response, error)
	mockDriveUpdateSheetProtectedDimension                     func(ctx context.Context, request *UpdateSheetProtectedDimensionReq, options ...MethodOptionFunc) (*UpdateSheetProtectedDimensionResp, *Response, error)
	mockDriveDeleteSheetProtectedDimension                     func(ctx context.Context, request *DeleteSheetProtectedDimensionReq, options ...MethodOptionFunc) (*DeleteSheetProtectedDimensionResp, *Response, error)
	mockDriveCreateSheetDataValidationDropdown                 func(ctx context.Context, request *CreateSheetDataValidationDropdownReq, options ...MethodOptionFunc) (*CreateSheetDataValidationDropdownResp, *Response, error)
	mockDriveDeleteSheetDataValidationDropdown                 func(ctx context.Context, request *DeleteSheetDataValidationDropdownReq, options ...MethodOptionFunc) (*DeleteSheetDataValidationDropdownResp, *Response, error)
	mockDriveUpdateSheetDataValidationDropdown                 func(ctx context.Context, request *UpdateSheetDataValidationDropdownReq, options ...MethodOptionFunc) (*UpdateSheetDataValidationDropdownResp, *Response, error)
	mockDriveGetSheetDataValidationDropdown                    func(ctx context.Context, request *GetSheetDataValidationDropdownReq, options ...MethodOptionFunc) (*GetSheetDataValidationDropdownResp, *Response, error)
	mockDriveCreateSheetFilter                                 func(ctx context.Context, request *CreateSheetFilterReq, options ...MethodOptionFunc) (*CreateSheetFilterResp, *Response, error)
	mockDriveDeleteSheetFilter                                 func(ctx context.Context, request *DeleteSheetFilterReq, options ...MethodOptionFunc) (*DeleteSheetFilterResp, *Response, error)
	mockDriveUpdateSheetFilter                                 func(ctx context.Context, request *UpdateSheetFilterReq, options ...MethodOptionFunc) (*UpdateSheetFilterResp, *Response, error)
	mockDriveGetSheetFilter                                    func(ctx context.Context, request *GetSheetFilterReq, options ...MethodOptionFunc) (*GetSheetFilterResp, *Response, error)
	mockDriveCreateSheetFilterView                             func(ctx context.Context, request *CreateSheetFilterViewReq, options ...MethodOptionFunc) (*CreateSheetFilterViewResp, *Response, error)
	mockDriveDeleteSheetFilterView                             func(ctx context.Context, request *DeleteSheetFilterViewReq, options ...MethodOptionFunc) (*DeleteSheetFilterViewResp, *Response, error)
	mockDriveUpdateSheetFilterView                             func(ctx context.Context, request *UpdateSheetFilterViewReq, options ...MethodOptionFunc) (*UpdateSheetFilterViewResp, *Response, error)
	mockDriveGetSheetFilterView                                func(ctx context.Context, request *GetSheetFilterViewReq, options ...MethodOptionFunc) (*GetSheetFilterViewResp, *Response, error)
	mockDriveQuerySheetFilterView                              func(ctx context.Context, request *QuerySheetFilterViewReq, options ...MethodOptionFunc) (*QuerySheetFilterViewResp, *Response, error)
	mockDriveCreateSheetFilterViewCondition                    func(ctx context.Context, request *CreateSheetFilterViewConditionReq, options ...MethodOptionFunc) (*CreateSheetFilterViewConditionResp, *Response, error)
	mockDriveDeleteSheetFilterViewCondition                    func(ctx context.Context, request *DeleteSheetFilterViewConditionReq, options ...MethodOptionFunc) (*DeleteSheetFilterViewConditionResp, *Response, error)
	mockDriveUpdateSheetFilterViewCondition                    func(ctx context.Context, request *UpdateSheetFilterViewConditionReq, options ...MethodOptionFunc) (*UpdateSheetFilterViewConditionResp, *Response, error)
	mockDriveGetSheetFilterViewCondition                       func(ctx context.Context, request *GetSheetFilterViewConditionReq, options ...MethodOptionFunc) (*GetSheetFilterViewConditionResp, *Response, error)
	mockDriveQuerySheetFilterViewCondition                     func(ctx context.Context, request *QuerySheetFilterViewConditionReq, options ...MethodOptionFunc) (*QuerySheetFilterViewConditionResp, *Response, error)
	mockDriveCreateSheetFloatImage                             func(ctx context.Context, request *CreateSheetFloatImageReq, options ...MethodOptionFunc) (*CreateSheetFloatImageResp, *Response, error)
	mockDriveDeleteSheetFloatImage                             func(ctx context.Context, request *DeleteSheetFloatImageReq, options ...MethodOptionFunc) (*DeleteSheetFloatImageResp, *Response, error)
	mockDriveUpdateSheetFloatImage                             func(ctx context.Context, request *UpdateSheetFloatImageReq, options ...MethodOptionFunc) (*UpdateSheetFloatImageResp, *Response, error)
	mockDriveGetSheetFloatImage                                func(ctx context.Context, request *GetSheetFloatImageReq, options ...MethodOptionFunc) (*GetSheetFloatImageResp, *Response, error)
	mockDriveQuerySheetFloatImage                              func(ctx context.Context, request *QuerySheetFloatImageReq, options ...MethodOptionFunc) (*QuerySheetFloatImageResp, *Response, error)
	mockDriveCreateWikiSpace                                   func(ctx context.Context, request *CreateWikiSpaceReq, options ...MethodOptionFunc) (*CreateWikiSpaceResp, *Response, error)
	mockDriveGetWikiSpaceList                                  func(ctx context.Context, request *GetWikiSpaceListReq, options ...MethodOptionFunc) (*GetWikiSpaceListResp, *Response, error)
	mockDriveGetWikiSpace                                      func(ctx context.Context, request *GetWikiSpaceReq, options ...MethodOptionFunc) (*GetWikiSpaceResp, *Response, error)
	mockDriveUpdateWikiSpaceSetting                            func(ctx context.Context, request *UpdateWikiSpaceSettingReq, options ...MethodOptionFunc) (*UpdateWikiSpaceSettingResp, *Response, error)
	mockDriveDeleteWikiSpaceMember                             func(ctx context.Context, request *DeleteWikiSpaceMemberReq, options ...MethodOptionFunc) (*DeleteWikiSpaceMemberResp, *Response, error)
	mockDriveAddWikiSpaceMember                                func(ctx context.Context, request *AddWikiSpaceMemberReq, options ...MethodOptionFunc) (*AddWikiSpaceMemberResp, *Response, error)
	mockDriveCreateWikiNode                                    func(ctx context.Context, request *CreateWikiNodeReq, options ...MethodOptionFunc) (*CreateWikiNodeResp, *Response, error)
	mockDriveGetWikiNodeList                                   func(ctx context.Context, request *GetWikiNodeListReq, options ...MethodOptionFunc) (*GetWikiNodeListResp, *Response, error)
	mockDriveMoveWikiNode                                      func(ctx context.Context, request *MoveWikiNodeReq, options ...MethodOptionFunc) (*MoveWikiNodeResp, *Response, error)
	mockDriveGetWikiNode                                       func(ctx context.Context, request *GetWikiNodeReq, options ...MethodOptionFunc) (*GetWikiNodeResp, *Response, error)
	mockDriveMoveDocsToWiki                                    func(ctx context.Context, request *MoveDocsToWikiReq, options ...MethodOptionFunc) (*MoveDocsToWikiResp, *Response, error)
	mockDriveGetWikiTask                                       func(ctx context.Context, request *GetWikiTaskReq, options ...MethodOptionFunc) (*GetWikiTaskResp, *Response, error)
	mockBitableGetBitableViewList                              func(ctx context.Context, request *GetBitableViewListReq, options ...MethodOptionFunc) (*GetBitableViewListResp, *Response, error)
	mockBitableCreateBitableView                               func(ctx context.Context, request *CreateBitableViewReq, options ...MethodOptionFunc) (*CreateBitableViewResp, *Response, error)
	mockBitableDeleteBitableView                               func(ctx context.Context, request *DeleteBitableViewReq, options ...MethodOptionFunc) (*DeleteBitableViewResp, *Response, error)
	mockBitableGetBitableRecordList                            func(ctx context.Context, request *GetBitableRecordListReq, options ...MethodOptionFunc) (*GetBitableRecordListResp, *Response, error)
	mockBitableGetBitableRecord                                func(ctx context.Context, request *GetBitableRecordReq, options ...MethodOptionFunc) (*GetBitableRecordResp, *Response, error)
	mockBitableCreateBitableRecord                             func(ctx context.Context, request *CreateBitableRecordReq, options ...MethodOptionFunc) (*CreateBitableRecordResp, *Response, error)
	mockBitableBatchCreateBitableRecord                        func(ctx context.Context, request *BatchCreateBitableRecordReq, options ...MethodOptionFunc) (*BatchCreateBitableRecordResp, *Response, error)
	mockBitableUpdateBitableRecord                             func(ctx context.Context, request *UpdateBitableRecordReq, options ...MethodOptionFunc) (*UpdateBitableRecordResp, *Response, error)
	mockBitableBatchUpdateBitableRecord                        func(ctx context.Context, request *BatchUpdateBitableRecordReq, options ...MethodOptionFunc) (*BatchUpdateBitableRecordResp, *Response, error)
	mockBitableDeleteBitableRecord                             func(ctx context.Context, request *DeleteBitableRecordReq, options ...MethodOptionFunc) (*DeleteBitableRecordResp, *Response, error)
	mockBitableBatchDeleteBitableRecord                        func(ctx context.Context, request *BatchDeleteBitableRecordReq, options ...MethodOptionFunc) (*BatchDeleteBitableRecordResp, *Response, error)
	mockBitableGetBitableFieldList                             func(ctx context.Context, request *GetBitableFieldListReq, options ...MethodOptionFunc) (*GetBitableFieldListResp, *Response, error)
	mockBitableCreateBitableField                              func(ctx context.Context, request *CreateBitableFieldReq, options ...MethodOptionFunc) (*CreateBitableFieldResp, *Response, error)
	mockBitableUpdateBitableField                              func(ctx context.Context, request *UpdateBitableFieldReq, options ...MethodOptionFunc) (*UpdateBitableFieldResp, *Response, error)
	mockBitableDeleteBitableField                              func(ctx context.Context, request *DeleteBitableFieldReq, options ...MethodOptionFunc) (*DeleteBitableFieldResp, *Response, error)
	mockBitableGetBitableAppRoleList                           func(ctx context.Context, request *GetBitableAppRoleListReq, options ...MethodOptionFunc) (*GetBitableAppRoleListResp, *Response, error)
	mockBitableCreateBitableAppRole                            func(ctx context.Context, request *CreateBitableAppRoleReq, options ...MethodOptionFunc) (*CreateBitableAppRoleResp, *Response, error)
	mockBitableDeleteBitableAppRole                            func(ctx context.Context, request *DeleteBitableAppRoleReq, options ...MethodOptionFunc) (*DeleteBitableAppRoleResp, *Response, error)
	mockBitableUpdateBitableAppRole                            func(ctx context.Context, request *UpdateBitableAppRoleReq, options ...MethodOptionFunc) (*UpdateBitableAppRoleResp, *Response, error)
	mockBitableGetBitableAppRoleMemberList                     func(ctx context.Context, request *GetBitableAppRoleMemberListReq, options ...MethodOptionFunc) (*GetBitableAppRoleMemberListResp, *Response, error)
	mockBitableCreateBitableAppRoleMember                      func(ctx context.Context, request *CreateBitableAppRoleMemberReq, options ...MethodOptionFunc) (*CreateBitableAppRoleMemberResp, *Response, error)
	mockBitableDeleteBitableAppRoleMember                      func(ctx context.Context, request *DeleteBitableAppRoleMemberReq, options ...MethodOptionFunc) (*DeleteBitableAppRoleMemberResp, *Response, error)
	mockBitableGetBitableTableList                             func(ctx context.Context, request *GetBitableTableListReq, options ...MethodOptionFunc) (*GetBitableTableListResp, *Response, error)
	mockBitableCreateBitableTable                              func(ctx context.Context, request *CreateBitableTableReq, options ...MethodOptionFunc) (*CreateBitableTableResp, *Response, error)
	mockBitableBatchCreateBitableTable                         func(ctx context.Context, request *BatchCreateBitableTableReq, options ...MethodOptionFunc) (*BatchCreateBitableTableResp, *Response, error)
	mockBitableDeleteBitableTable                              func(ctx context.Context, request *DeleteBitableTableReq, options ...MethodOptionFunc) (*DeleteBitableTableResp, *Response, error)
	mockBitableBatchDeleteBitableTable                         func(ctx context.Context, request *BatchDeleteBitableTableReq, options ...MethodOptionFunc) (*BatchDeleteBitableTableResp, *Response, error)
	mockBitableUpdateBitableMeta                               func(ctx context.Context, request *UpdateBitableMetaReq, options ...MethodOptionFunc) (*UpdateBitableMetaResp, *Response, error)
	mockBitableGetBitableMeta                                  func(ctx context.Context, request *GetBitableMetaReq, options ...MethodOptionFunc) (*GetBitableMetaResp, *Response, error)
	mockMeetingRoomGetMeetingRoomCustomization                 func(ctx context.Context, request *GetMeetingRoomCustomizationReq, options ...MethodOptionFunc) (*GetMeetingRoomCustomizationResp, *Response, error)
	mockMeetingRoomBatchGetMeetingRoomSummary                  func(ctx context.Context, request *BatchGetMeetingRoomSummaryReq, options ...MethodOptionFunc) (*BatchGetMeetingRoomSummaryResp, *Response, error)
	mockMeetingRoomGetMeetingRoomBuildingList                  func(ctx context.Context, request *GetMeetingRoomBuildingListReq, options ...MethodOptionFunc) (*GetMeetingRoomBuildingListResp, *Response, error)
	mockMeetingRoomBatchGetMeetingRoomBuilding                 func(ctx context.Context, request *BatchGetMeetingRoomBuildingReq, options ...MethodOptionFunc) (*BatchGetMeetingRoomBuildingResp, *Response, error)
	mockMeetingRoomGetMeetingRoomRoomList                      func(ctx context.Context, request *GetMeetingRoomRoomListReq, options ...MethodOptionFunc) (*GetMeetingRoomRoomListResp, *Response, error)
	mockMeetingRoomBatchGetMeetingRoomRoom                     func(ctx context.Context, request *BatchGetMeetingRoomRoomReq, options ...MethodOptionFunc) (*BatchGetMeetingRoomRoomResp, *Response, error)
	mockMeetingRoomBatchGetMeetingRoomFreebusy                 func(ctx context.Context, request *BatchGetMeetingRoomFreebusyReq, options ...MethodOptionFunc) (*BatchGetMeetingRoomFreebusyResp, *Response, error)
	mockMeetingRoomReplyMeetingRoomInstance                    func(ctx context.Context, request *ReplyMeetingRoomInstanceReq, options ...MethodOptionFunc) (*ReplyMeetingRoomInstanceResp, *Response, error)
	mockMeetingRoomCreateMeetingRoomBuilding                   func(ctx context.Context, request *CreateMeetingRoomBuildingReq, options ...MethodOptionFunc) (*CreateMeetingRoomBuildingResp, *Response, error)
	mockMeetingRoomUpdateMeetingRoomBuilding                   func(ctx context.Context, request *UpdateMeetingRoomBuildingReq, options ...MethodOptionFunc) (*UpdateMeetingRoomBuildingResp, *Response, error)
	mockMeetingRoomDeleteMeetingRoomBuilding                   func(ctx context.Context, request *DeleteMeetingRoomBuildingReq, options ...MethodOptionFunc) (*DeleteMeetingRoomBuildingResp, *Response, error)
	mockMeetingRoomBatchGetMeetingRoomBuildingID               func(ctx context.Context, request *BatchGetMeetingRoomBuildingIDReq, options ...MethodOptionFunc) (*BatchGetMeetingRoomBuildingIDResp, *Response, error)
	mockMeetingRoomCreateMeetingRoomRoom                       func(ctx context.Context, request *CreateMeetingRoomRoomReq, options ...MethodOptionFunc) (*CreateMeetingRoomRoomResp, *Response, error)
	mockMeetingRoomUpdateMeetingRoomRoom                       func(ctx context.Context, request *UpdateMeetingRoomRoomReq, options ...MethodOptionFunc) (*UpdateMeetingRoomRoomResp, *Response, error)
	mockMeetingRoomDeleteMeetingRoomRoom                       func(ctx context.Context, request *DeleteMeetingRoomRoomReq, options ...MethodOptionFunc) (*DeleteMeetingRoomRoomResp, *Response, error)
	mockMeetingRoomBatchGetMeetingRoomRoomID                   func(ctx context.Context, request *BatchGetMeetingRoomRoomIDReq, options ...MethodOptionFunc) (*BatchGetMeetingRoomRoomIDResp, *Response, error)
	mockMeetingRoomGetMeetingRoomCountryList                   func(ctx context.Context, request *GetMeetingRoomCountryListReq, options ...MethodOptionFunc) (*GetMeetingRoomCountryListResp, *Response, error)
	mockMeetingRoomGetMeetingRoomDistrictList                  func(ctx context.Context, request *GetMeetingRoomDistrictListReq, options ...MethodOptionFunc) (*GetMeetingRoomDistrictListResp, *Response, error)
	mockJssdkGetJssdkTicket                                    func(ctx context.Context, request *GetJssdkTicketReq, options ...MethodOptionFunc) (*GetJssdkTicketResp, *Response, error)
	mockVCApplyVCReserve                                       func(ctx context.Context, request *ApplyVCReserveReq, options ...MethodOptionFunc) (*ApplyVCReserveResp, *Response, error)
	mockVCUpdateVCReserve                                      func(ctx context.Context, request *UpdateVCReserveReq, options ...MethodOptionFunc) (*UpdateVCReserveResp, *Response, error)
	mockVCDeleteVCReserve                                      func(ctx context.Context, request *DeleteVCReserveReq, options ...MethodOptionFunc) (*DeleteVCReserveResp, *Response, error)
	mockVCGetVCReserve                                         func(ctx context.Context, request *GetVCReserveReq, options ...MethodOptionFunc) (*GetVCReserveResp, *Response, error)
	mockVCGetVCReserveActiveMeeting                            func(ctx context.Context, request *GetVCReserveActiveMeetingReq, options ...MethodOptionFunc) (*GetVCReserveActiveMeetingResp, *Response, error)
	mockVCGetVCMeeting                                         func(ctx context.Context, request *GetVCMeetingReq, options ...MethodOptionFunc) (*GetVCMeetingResp, *Response, error)
	mockVCListVCMeetingByNo                                    func(ctx context.Context, request *ListVCMeetingByNoReq, options ...MethodOptionFunc) (*ListVCMeetingByNoResp, *Response, error)
	mockVCInviteVCMeeting                                      func(ctx context.Context, request *InviteVCMeetingReq, options ...MethodOptionFunc) (*InviteVCMeetingResp, *Response, error)
	mockVCKickoutVCMeeting                                     func(ctx context.Context, request *KickoutVCMeetingReq, options ...MethodOptionFunc) (*KickoutVCMeetingResp, *Response, error)
	mockVCSetVCHostMeeting                                     func(ctx context.Context, request *SetVCHostMeetingReq, options ...MethodOptionFunc) (*SetVCHostMeetingResp, *Response, error)
	mockVCEndVCMeeting                                         func(ctx context.Context, request *EndVCMeetingReq, options ...MethodOptionFunc) (*EndVCMeetingResp, *Response, error)
	mockVCStartVCMeetingRecording                              func(ctx context.Context, request *StartVCMeetingRecordingReq, options ...MethodOptionFunc) (*StartVCMeetingRecordingResp, *Response, error)
	mockVCStopVCMeetingRecording                               func(ctx context.Context, request *StopVCMeetingRecordingReq, options ...MethodOptionFunc) (*StopVCMeetingRecordingResp, *Response, error)
	mockVCGetVCMeetingRecording                                func(ctx context.Context, request *GetVCMeetingRecordingReq, options ...MethodOptionFunc) (*GetVCMeetingRecordingResp, *Response, error)
	mockVCSetVCPermissionMeetingRecording                      func(ctx context.Context, request *SetVCPermissionMeetingRecordingReq, options ...MethodOptionFunc) (*SetVCPermissionMeetingRecordingResp, *Response, error)
	mockVCGetVCDailyReport                                     func(ctx context.Context, request *GetVCDailyReportReq, options ...MethodOptionFunc) (*GetVCDailyReportResp, *Response, error)
	mockVCGetVCTopUserReport                                   func(ctx context.Context, request *GetVCTopUserReportReq, options ...MethodOptionFunc) (*GetVCTopUserReportResp, *Response, error)
	mockVCGetVCRoomConfig                                      func(ctx context.Context, request *GetVCRoomConfigReq, options ...MethodOptionFunc) (*GetVCRoomConfigResp, *Response, error)
	mockVCSetVCRoomConfig                                      func(ctx context.Context, request *SetVCRoomConfigReq, options ...MethodOptionFunc) (*SetVCRoomConfigResp, *Response, error)
	mockApplicationIsApplicationUserAdmin                      func(ctx context.Context, request *IsApplicationUserAdminReq, options ...MethodOptionFunc) (*IsApplicationUserAdminResp, *Response, error)
	mockApplicationGetApplicationUserAdminScope                func(ctx context.Context, request *GetApplicationUserAdminScopeReq, options ...MethodOptionFunc) (*GetApplicationUserAdminScopeResp, *Response, error)
	mockApplicationGetApplicationAppVisibility                 func(ctx context.Context, request *GetApplicationAppVisibilityReq, options ...MethodOptionFunc) (*GetApplicationAppVisibilityResp, *Response, error)
	mockApplicationGetApplicationUserVisibleApp                func(ctx context.Context, request *GetApplicationUserVisibleAppReq, options ...MethodOptionFunc) (*GetApplicationUserVisibleAppResp, *Response, error)
	mockApplicationGetApplicationAppList                       func(ctx context.Context, request *GetApplicationAppListReq, options ...MethodOptionFunc) (*GetApplicationAppListResp, *Response, error)
	mockApplicationUpdateApplicationAppVisibility              func(ctx context.Context, request *UpdateApplicationAppVisibilityReq, options ...MethodOptionFunc) (*UpdateApplicationAppVisibilityResp, *Response, error)
	mockApplicationGetApplicationAppAdminUserList              func(ctx context.Context, request *GetApplicationAppAdminUserListReq, options ...MethodOptionFunc) (*GetApplicationAppAdminUserListResp, *Response, error)
	mockApplicationCheckUserIsInApplicationPaidScope           func(ctx context.Context, request *CheckUserIsInApplicationPaidScopeReq, options ...MethodOptionFunc) (*CheckUserIsInApplicationPaidScopeResp, *Response, error)
	mockApplicationGetApplicationOrderList                     func(ctx context.Context, request *GetApplicationOrderListReq, options ...MethodOptionFunc) (*GetApplicationOrderListResp, *Response, error)
	mockApplicationGetApplicationOrder                         func(ctx context.Context, request *GetApplicationOrderReq, options ...MethodOptionFunc) (*GetApplicationOrderResp, *Response, error)
	mockApplicationGetApplicationUnderAuditList                func(ctx context.Context, request *GetApplicationUnderAuditListReq, options ...MethodOptionFunc) (*GetApplicationUnderAuditListResp, *Response, error)
	mockApplicationGetApplication                              func(ctx context.Context, request *GetApplicationReq, options ...MethodOptionFunc) (*GetApplicationResp, *Response, error)
	mockApplicationGetApplicationVersion                       func(ctx context.Context, request *GetApplicationVersionReq, options ...MethodOptionFunc) (*GetApplicationVersionResp, *Response, error)
	mockApplicationUpdateApplicationVersion                    func(ctx context.Context, request *UpdateApplicationVersionReq, options ...MethodOptionFunc) (*UpdateApplicationVersionResp, *Response, error)
	mockApplicationUpdateApplication                           func(ctx context.Context, request *UpdateApplicationReq, options ...MethodOptionFunc) (*UpdateApplicationResp, *Response, error)
	mockApplicationGetApplicationUsageOverview                 func(ctx context.Context, request *GetApplicationUsageOverviewReq, options ...MethodOptionFunc) (*GetApplicationUsageOverviewResp, *Response, error)
	mockApplicationGetApplicationUsageTrend                    func(ctx context.Context, request *GetApplicationUsageTrendReq, options ...MethodOptionFunc) (*GetApplicationUsageTrendResp, *Response, error)
	mockApplicationUpdateApplicationFeedback                   func(ctx context.Context, request *UpdateApplicationFeedbackReq, options ...MethodOptionFunc) (*UpdateApplicationFeedbackResp, *Response, error)
	mockApplicationGetApplicationFeedbackList                  func(ctx context.Context, request *GetApplicationFeedbackListReq, options ...MethodOptionFunc) (*GetApplicationFeedbackListResp, *Response, error)
	mockMailGetMailUser                                        func(ctx context.Context, request *GetMailUserReq, options ...MethodOptionFunc) (*GetMailUserResp, *Response, error)
	mockMailCreateMailGroup                                    func(ctx context.Context, request *CreateMailGroupReq, options ...MethodOptionFunc) (*CreateMailGroupResp, *Response, error)
	mockMailGetMailGroup                                       func(ctx context.Context, request *GetMailGroupReq, options ...MethodOptionFunc) (*GetMailGroupResp, *Response, error)
	mockMailGetMailGroupList                                   func(ctx context.Context, request *GetMailGroupListReq, options ...MethodOptionFunc) (*GetMailGroupListResp, *Response, error)
	mockMailUpdateMailGroupPatch                               func(ctx context.Context, request *UpdateMailGroupPatchReq, options ...MethodOptionFunc) (*UpdateMailGroupPatchResp, *Response, error)
	mockMailUpdateMailGroup                                    func(ctx context.Context, request *UpdateMailGroupReq, options ...MethodOptionFunc) (*UpdateMailGroupResp, *Response, error)
	mockMailDeleteMailGroup                                    func(ctx context.Context, request *DeleteMailGroupReq, options ...MethodOptionFunc) (*DeleteMailGroupResp, *Response, error)
	mockMailCreateMailGroupMember                              func(ctx context.Context, request *CreateMailGroupMemberReq, options ...MethodOptionFunc) (*CreateMailGroupMemberResp, *Response, error)
	mockMailGetMailGroupMember                                 func(ctx context.Context, request *GetMailGroupMemberReq, options ...MethodOptionFunc) (*GetMailGroupMemberResp, *Response, error)
	mockMailGetMailGroupMemberList                             func(ctx context.Context, request *GetMailGroupMemberListReq, options ...MethodOptionFunc) (*GetMailGroupMemberListResp, *Response, error)
	mockMailDeleteMailGroupMember                              func(ctx context.Context, request *DeleteMailGroupMemberReq, options ...MethodOptionFunc) (*DeleteMailGroupMemberResp, *Response, error)
	mockMailCreateMailGroupPermissionMember                    func(ctx context.Context, request *CreateMailGroupPermissionMemberReq, options ...MethodOptionFunc) (*CreateMailGroupPermissionMemberResp, *Response, error)
	mockMailGetMailGroupPermissionMember                       func(ctx context.Context, request *GetMailGroupPermissionMemberReq, options ...MethodOptionFunc) (*GetMailGroupPermissionMemberResp, *Response, error)
	mockMailGetMailGroupPermissionMemberList                   func(ctx context.Context, request *GetMailGroupPermissionMemberListReq, options ...MethodOptionFunc) (*GetMailGroupPermissionMemberListResp, *Response, error)
	mockMailDeleteMailGroupPermissionMember                    func(ctx context.Context, request *DeleteMailGroupPermissionMemberReq, options ...MethodOptionFunc) (*DeleteMailGroupPermissionMemberResp, *Response, error)
	mockMailCreateMailGroupAlias                               func(ctx context.Context, request *CreateMailGroupAliasReq, options ...MethodOptionFunc) (*CreateMailGroupAliasResp, *Response, error)
	mockMailGetMailGroupAliasList                              func(ctx context.Context, request *GetMailGroupAliasListReq, options ...MethodOptionFunc) (*GetMailGroupAliasListResp, *Response, error)
	mockMailDeleteMailGroupAlias                               func(ctx context.Context, request *DeleteMailGroupAliasReq, options ...MethodOptionFunc) (*DeleteMailGroupAliasResp, *Response, error)
	mockMailCreatePublicMailbox                                func(ctx context.Context, request *CreatePublicMailboxReq, options ...MethodOptionFunc) (*CreatePublicMailboxResp, *Response, error)
	mockMailGetPublicMailbox                                   func(ctx context.Context, request *GetPublicMailboxReq, options ...MethodOptionFunc) (*GetPublicMailboxResp, *Response, error)
	mockMailGetPublicMailboxList                               func(ctx context.Context, request *GetPublicMailboxListReq, options ...MethodOptionFunc) (*GetPublicMailboxListResp, *Response, error)
	mockMailUpdatePublicMailboxPatch                           func(ctx context.Context, request *UpdatePublicMailboxPatchReq, options ...MethodOptionFunc) (*UpdatePublicMailboxPatchResp, *Response, error)
	mockMailUpdatePublicMailbox                                func(ctx context.Context, request *UpdatePublicMailboxReq, options ...MethodOptionFunc) (*UpdatePublicMailboxResp, *Response, error)
	mockMailDeletePublicMailbox                                func(ctx context.Context, request *DeletePublicMailboxReq, options ...MethodOptionFunc) (*DeletePublicMailboxResp, *Response, error)
	mockMailCreatePublicMailboxMember                          func(ctx context.Context, request *CreatePublicMailboxMemberReq, options ...MethodOptionFunc) (*CreatePublicMailboxMemberResp, *Response, error)
	mockMailGetPublicMailboxMember                             func(ctx context.Context, request *GetPublicMailboxMemberReq, options ...MethodOptionFunc) (*GetPublicMailboxMemberResp, *Response, error)
	mockMailGetPublicMailboxMemberList                         func(ctx context.Context, request *GetPublicMailboxMemberListReq, options ...MethodOptionFunc) (*GetPublicMailboxMemberListResp, *Response, error)
	mockMailDeletePublicMailboxMember                          func(ctx context.Context, request *DeletePublicMailboxMemberReq, options ...MethodOptionFunc) (*DeletePublicMailboxMemberResp, *Response, error)
	mockMailClearPublicMailboxMember                           func(ctx context.Context, request *ClearPublicMailboxMemberReq, options ...MethodOptionFunc) (*ClearPublicMailboxMemberResp, *Response, error)
	mockMailCreateMailPublicMailboxAlias                       func(ctx context.Context, request *CreateMailPublicMailboxAliasReq, options ...MethodOptionFunc) (*CreateMailPublicMailboxAliasResp, *Response, error)
	mockMailGetMailPublicMailboxAliasList                      func(ctx context.Context, request *GetMailPublicMailboxAliasListReq, options ...MethodOptionFunc) (*GetMailPublicMailboxAliasListResp, *Response, error)
	mockMailDeleteMailPublicMailboxAlias                       func(ctx context.Context, request *DeleteMailPublicMailboxAliasReq, options ...MethodOptionFunc) (*DeleteMailPublicMailboxAliasResp, *Response, error)
	mockMailCreateMailUserMailboxAlias                         func(ctx context.Context, request *CreateMailUserMailboxAliasReq, options ...MethodOptionFunc) (*CreateMailUserMailboxAliasResp, *Response, error)
	mockMailDeleteMailUserMailboxAlias                         func(ctx context.Context, request *DeleteMailUserMailboxAliasReq, options ...MethodOptionFunc) (*DeleteMailUserMailboxAliasResp, *Response, error)
	mockMailGetMailUserMailboxAliasList                        func(ctx context.Context, request *GetMailUserMailboxAliasListReq, options ...MethodOptionFunc) (*GetMailUserMailboxAliasListResp, *Response, error)
	mockMailDeleteMailUserMailbox                              func(ctx context.Context, request *DeleteMailUserMailboxReq, options ...MethodOptionFunc) (*DeleteMailUserMailboxResp, *Response, error)
	mockApprovalGetApproval                                    func(ctx context.Context, request *GetApprovalReq, options ...MethodOptionFunc) (*GetApprovalResp, *Response, error)
	mockApprovalGetApprovalInstanceList                        func(ctx context.Context, request *GetApprovalInstanceListReq, options ...MethodOptionFunc) (*GetApprovalInstanceListResp, *Response, error)
	mockApprovalGetApprovalInstance                            func(ctx context.Context, request *GetApprovalInstanceReq, options ...MethodOptionFunc) (*GetApprovalInstanceResp, *Response, error)
	mockApprovalCreateApprovalInstance                         func(ctx context.Context, request *CreateApprovalInstanceReq, options ...MethodOptionFunc) (*CreateApprovalInstanceResp, *Response, error)
	mockApprovalApproveApprovalInstance                        func(ctx context.Context, request *ApproveApprovalInstanceReq, options ...MethodOptionFunc) (*ApproveApprovalInstanceResp, *Response, error)
	mockApprovalRejectApprovalInstance                         func(ctx context.Context, request *RejectApprovalInstanceReq, options ...MethodOptionFunc) (*RejectApprovalInstanceResp, *Response, error)
	mockApprovalTransferApprovalInstance                       func(ctx context.Context, request *TransferApprovalInstanceReq, options ...MethodOptionFunc) (*TransferApprovalInstanceResp, *Response, error)
	mockApprovalRollbackApprovalInstance                       func(ctx context.Context, request *RollbackApprovalInstanceReq, options ...MethodOptionFunc) (*RollbackApprovalInstanceResp, *Response, error)
	mockApprovalCancelApprovalInstance                         func(ctx context.Context, request *CancelApprovalInstanceReq, options ...MethodOptionFunc) (*CancelApprovalInstanceResp, *Response, error)
	mockApprovalSearchApprovalInstance                         func(ctx context.Context, request *SearchApprovalInstanceReq, options ...MethodOptionFunc) (*SearchApprovalInstanceResp, *Response, error)
	mockApprovalAddApprovalInstanceSign                        func(ctx context.Context, request *AddApprovalInstanceSignReq, options ...MethodOptionFunc) (*AddApprovalInstanceSignResp, *Response, error)
	mockApprovalUploadApprovalFile                             func(ctx context.Context, request *UploadApprovalFileReq, options ...MethodOptionFunc) (*UploadApprovalFileResp, *Response, error)
	mockApprovalSearchApprovalTask                             func(ctx context.Context, request *SearchApprovalTaskReq, options ...MethodOptionFunc) (*SearchApprovalTaskResp, *Response, error)
	mockApprovalGetApprovalUserTaskList                        func(ctx context.Context, request *GetApprovalUserTaskListReq, options ...MethodOptionFunc) (*GetApprovalUserTaskListResp, *Response, error)
	mockApprovalSearchApprovalCarbonCopy                       func(ctx context.Context, request *SearchApprovalCarbonCopyReq, options ...MethodOptionFunc) (*SearchApprovalCarbonCopyResp, *Response, error)
	mockApprovalCreateApprovalCarbonCopy                       func(ctx context.Context, request *CreateApprovalCarbonCopyReq, options ...MethodOptionFunc) (*CreateApprovalCarbonCopyResp, *Response, error)
	mockApprovalPreviewApprovalInstance                        func(ctx context.Context, request *PreviewApprovalInstanceReq, options ...MethodOptionFunc) (*PreviewApprovalInstanceResp, *Response, error)
	mockApprovalUpdateApprovalMessage                          func(ctx context.Context, request *UpdateApprovalMessageReq, options ...MethodOptionFunc) (*UpdateApprovalMessageResp, *Response, error)
	mockApprovalSubscribeApprovalSubscription                  func(ctx context.Context, request *SubscribeApprovalSubscriptionReq, options ...MethodOptionFunc) (*SubscribeApprovalSubscriptionResp, *Response, error)
	mockApprovalUnsubscribeApprovalSubscription                func(ctx context.Context, request *UnsubscribeApprovalSubscriptionReq, options ...MethodOptionFunc) (*UnsubscribeApprovalSubscriptionResp, *Response, error)
	mockApprovalGetApprovalExternalList                        func(ctx context.Context, request *GetApprovalExternalListReq, options ...MethodOptionFunc) (*GetApprovalExternalListResp, *Response, error)
	mockApprovalSendApprovalMessage                            func(ctx context.Context, request *SendApprovalMessageReq, options ...MethodOptionFunc) (*SendApprovalMessageResp, *Response, error)
	mockHelpdeskCreateHelpdeskNotification                     func(ctx context.Context, request *CreateHelpdeskNotificationReq, options ...MethodOptionFunc) (*CreateHelpdeskNotificationResp, *Response, error)
	mockHelpdeskUpdateHelpdeskNotification                     func(ctx context.Context, request *UpdateHelpdeskNotificationReq, options ...MethodOptionFunc) (*UpdateHelpdeskNotificationResp, *Response, error)
	mockHelpdeskGetHelpdeskNotification                        func(ctx context.Context, request *GetHelpdeskNotificationReq, options ...MethodOptionFunc) (*GetHelpdeskNotificationResp, *Response, error)
	mockHelpdeskPreviewHelpdeskNotification                    func(ctx context.Context, request *PreviewHelpdeskNotificationReq, options ...MethodOptionFunc) (*PreviewHelpdeskNotificationResp, *Response, error)
	mockHelpdeskSubmitApproveHelpdeskNotification              func(ctx context.Context, request *SubmitApproveHelpdeskNotificationReq, options ...MethodOptionFunc) (*SubmitApproveHelpdeskNotificationResp, *Response, error)
	mockHelpdeskCancelApproveHelpdeskNotification              func(ctx context.Context, request *CancelApproveHelpdeskNotificationReq, options ...MethodOptionFunc) (*CancelApproveHelpdeskNotificationResp, *Response, error)
	mockHelpdeskExecuteSendHelpdeskNotification                func(ctx context.Context, request *ExecuteSendHelpdeskNotificationReq, options ...MethodOptionFunc) (*ExecuteSendHelpdeskNotificationResp, *Response, error)
	mockHelpdeskCancelSendHelpdeskNotification                 func(ctx context.Context, request *CancelSendHelpdeskNotificationReq, options ...MethodOptionFunc) (*CancelSendHelpdeskNotificationResp, *Response, error)
	mockHelpdeskStartHelpdeskService                           func(ctx context.Context, request *StartHelpdeskServiceReq, options ...MethodOptionFunc) (*StartHelpdeskServiceResp, *Response, error)
	mockHelpdeskGetHelpdeskTicket                              func(ctx context.Context, request *GetHelpdeskTicketReq, options ...MethodOptionFunc) (*GetHelpdeskTicketResp, *Response, error)
	mockHelpdeskUpdateHelpdeskTicket                           func(ctx context.Context, request *UpdateHelpdeskTicketReq, options ...MethodOptionFunc) (*UpdateHelpdeskTicketResp, *Response, error)
	mockHelpdeskGetHelpdeskTicketList                          func(ctx context.Context, request *GetHelpdeskTicketListReq, options ...MethodOptionFunc) (*GetHelpdeskTicketListResp, *Response, error)
	mockHelpdeskDownloadHelpdeskTicketImage                    func(ctx context.Context, request *DownloadHelpdeskTicketImageReq, options ...MethodOptionFunc) (*DownloadHelpdeskTicketImageResp, *Response, error)
	mockHelpdeskAnswerHelpdeskTicketUserQuery                  func(ctx context.Context, request *AnswerHelpdeskTicketUserQueryReq, options ...MethodOptionFunc) (*AnswerHelpdeskTicketUserQueryResp, *Response, error)
	mockHelpdeskGetHelpdeskTicketCustomizedFields              func(ctx context.Context, request *GetHelpdeskTicketCustomizedFieldsReq, options ...MethodOptionFunc) (*GetHelpdeskTicketCustomizedFieldsResp, *Response, error)
	mockHelpdeskGetHelpdeskTicketMessageList                   func(ctx context.Context, request *GetHelpdeskTicketMessageListReq, options ...MethodOptionFunc) (*GetHelpdeskTicketMessageListResp, *Response, error)
	mockHelpdeskSendHelpdeskTicketMessage                      func(ctx context.Context, request *SendHelpdeskTicketMessageReq, options ...MethodOptionFunc) (*SendHelpdeskTicketMessageResp, *Response, error)
	mockHelpdeskSendHelpdeskMessage                            func(ctx context.Context, request *SendHelpdeskMessageReq, options ...MethodOptionFunc) (*SendHelpdeskMessageResp, *Response, error)
	mockHelpdeskGetHelpdeskTicketCustomizedFieldList           func(ctx context.Context, request *GetHelpdeskTicketCustomizedFieldListReq, options ...MethodOptionFunc) (*GetHelpdeskTicketCustomizedFieldListResp, *Response, error)
	mockHelpdeskDeleteHelpdeskTicketCustomizedField            func(ctx context.Context, request *DeleteHelpdeskTicketCustomizedFieldReq, options ...MethodOptionFunc) (*DeleteHelpdeskTicketCustomizedFieldResp, *Response, error)
	mockHelpdeskUpdateHelpdeskTicketCustomizedField            func(ctx context.Context, request *UpdateHelpdeskTicketCustomizedFieldReq, options ...MethodOptionFunc) (*UpdateHelpdeskTicketCustomizedFieldResp, *Response, error)
	mockHelpdeskCreateHelpdeskTicketCustomizedField            func(ctx context.Context, request *CreateHelpdeskTicketCustomizedFieldReq, options ...MethodOptionFunc) (*CreateHelpdeskTicketCustomizedFieldResp, *Response, error)
	mockHelpdeskGetHelpdeskTicketCustomizedField               func(ctx context.Context, request *GetHelpdeskTicketCustomizedFieldReq, options ...MethodOptionFunc) (*GetHelpdeskTicketCustomizedFieldResp, *Response, error)
	mockHelpdeskCreateHelpdeskCategory                         func(ctx context.Context, request *CreateHelpdeskCategoryReq, options ...MethodOptionFunc) (*CreateHelpdeskCategoryResp, *Response, error)
	mockHelpdeskGetHelpdeskCategory                            func(ctx context.Context, request *GetHelpdeskCategoryReq, options ...MethodOptionFunc) (*GetHelpdeskCategoryResp, *Response, error)
	mockHelpdeskUpdateHelpdeskCategory                         func(ctx context.Context, request *UpdateHelpdeskCategoryReq, options ...MethodOptionFunc) (*UpdateHelpdeskCategoryResp, *Response, error)
	mockHelpdeskDeleteHelpdeskCategory                         func(ctx context.Context, request *DeleteHelpdeskCategoryReq, options ...MethodOptionFunc) (*DeleteHelpdeskCategoryResp, *Response, error)
	mockHelpdeskGetHelpdeskCategoryList                        func(ctx context.Context, request *GetHelpdeskCategoryListReq, options ...MethodOptionFunc) (*GetHelpdeskCategoryListResp, *Response, error)
	mockHelpdeskCreateHelpdeskFAQ                              func(ctx context.Context, request *CreateHelpdeskFAQReq, options ...MethodOptionFunc) (*CreateHelpdeskFAQResp, *Response, error)
	mockHelpdeskGetHelpdeskFAQ                                 func(ctx context.Context, request *GetHelpdeskFAQReq, options ...MethodOptionFunc) (*GetHelpdeskFAQResp, *Response, error)
	mockHelpdeskUpdateHelpdeskFAQ                              func(ctx context.Context, request *UpdateHelpdeskFAQReq, options ...MethodOptionFunc) (*UpdateHelpdeskFAQResp, *Response, error)
	mockHelpdeskDeleteHelpdeskFAQ                              func(ctx context.Context, request *DeleteHelpdeskFAQReq, options ...MethodOptionFunc) (*DeleteHelpdeskFAQResp, *Response, error)
	mockHelpdeskGetHelpdeskFAQList                             func(ctx context.Context, request *GetHelpdeskFAQListReq, options ...MethodOptionFunc) (*GetHelpdeskFAQListResp, *Response, error)
	mockHelpdeskGetHelpdeskFAQImage                            func(ctx context.Context, request *GetHelpdeskFAQImageReq, options ...MethodOptionFunc) (*GetHelpdeskFAQImageResp, *Response, error)
	mockHelpdeskSearchHelpdeskFAQ                              func(ctx context.Context, request *SearchHelpdeskFAQReq, options ...MethodOptionFunc) (*SearchHelpdeskFAQResp, *Response, error)
	mockHelpdeskUpdateHelpdeskAgent                            func(ctx context.Context, request *UpdateHelpdeskAgentReq, options ...MethodOptionFunc) (*UpdateHelpdeskAgentResp, *Response, error)
	mockHelpdeskGetHelpdeskAgentEmail                          func(ctx context.Context, request *GetHelpdeskAgentEmailReq, options ...MethodOptionFunc) (*GetHelpdeskAgentEmailResp, *Response, error)
	mockHelpdeskCreateHelpdeskAgentSchedule                    func(ctx context.Context, request *CreateHelpdeskAgentScheduleReq, options ...MethodOptionFunc) (*CreateHelpdeskAgentScheduleResp, *Response, error)
	mockHelpdeskDeleteHelpdeskAgentSchedule                    func(ctx context.Context, request *DeleteHelpdeskAgentScheduleReq, options ...MethodOptionFunc) (*DeleteHelpdeskAgentScheduleResp, *Response, error)
	mockHelpdeskUpdateHelpdeskAgentSchedule                    func(ctx context.Context, request *UpdateHelpdeskAgentScheduleReq, options ...MethodOptionFunc) (*UpdateHelpdeskAgentScheduleResp, *Response, error)
	mockHelpdeskGetHelpdeskAgentSchedule                       func(ctx context.Context, request *GetHelpdeskAgentScheduleReq, options ...MethodOptionFunc) (*GetHelpdeskAgentScheduleResp, *Response, error)
	mockHelpdeskGetHelpdeskAgentScheduleList                   func(ctx context.Context, request *GetHelpdeskAgentScheduleListReq, options ...MethodOptionFunc) (*GetHelpdeskAgentScheduleListResp, *Response, error)
	mockHelpdeskCreateHelpdeskAgentSkill                       func(ctx context.Context, request *CreateHelpdeskAgentSkillReq, options ...MethodOptionFunc) (*CreateHelpdeskAgentSkillResp, *Response, error)
	mockHelpdeskGetHelpdeskAgentSkill                          func(ctx context.Context, request *GetHelpdeskAgentSkillReq, options ...MethodOptionFunc) (*GetHelpdeskAgentSkillResp, *Response, error)
	mockHelpdeskUpdateHelpdeskAgentSkill                       func(ctx context.Context, request *UpdateHelpdeskAgentSkillReq, options ...MethodOptionFunc) (*UpdateHelpdeskAgentSkillResp, *Response, error)
	mockHelpdeskDeleteHelpdeskAgentSkill                       func(ctx context.Context, request *DeleteHelpdeskAgentSkillReq, options ...MethodOptionFunc) (*DeleteHelpdeskAgentSkillResp, *Response, error)
	mockHelpdeskGetHelpdeskAgentSkillList                      func(ctx context.Context, request *GetHelpdeskAgentSkillListReq, options ...MethodOptionFunc) (*GetHelpdeskAgentSkillListResp, *Response, error)
	mockHelpdeskGetHelpdeskAgentSkillRuleList                  func(ctx context.Context, request *GetHelpdeskAgentSkillRuleListReq, options ...MethodOptionFunc) (*GetHelpdeskAgentSkillRuleListResp, *Response, error)
	mockHelpdeskSubscribeHelpdeskEvent                         func(ctx context.Context, request *SubscribeHelpdeskEventReq, options ...MethodOptionFunc) (*SubscribeHelpdeskEventResp, *Response, error)
	mockHelpdeskUnsubscribeHelpdeskEvent                       func(ctx context.Context, request *UnsubscribeHelpdeskEventReq, options ...MethodOptionFunc) (*UnsubscribeHelpdeskEventResp, *Response, error)
	mockAdminAdminResetPassword                                func(ctx context.Context, request *AdminResetPasswordReq, options ...MethodOptionFunc) (*AdminResetPasswordResp, *Response, error)
	mockAdminGetAdminDeptStats                                 func(ctx context.Context, request *GetAdminDeptStatsReq, options ...MethodOptionFunc) (*GetAdminDeptStatsResp, *Response, error)
	mockAdminGetAdminUserStats                                 func(ctx context.Context, request *GetAdminUserStatsReq, options ...MethodOptionFunc) (*GetAdminUserStatsResp, *Response, error)
	mockHumanAuthGetFaceVerifyAuthResult                       func(ctx context.Context, request *GetFaceVerifyAuthResultReq, options ...MethodOptionFunc) (*GetFaceVerifyAuthResultResp, *Response, error)
	mockHumanAuthUploadFaceVerifyImage                         func(ctx context.Context, request *UploadFaceVerifyImageReq, options ...MethodOptionFunc) (*UploadFaceVerifyImageResp, *Response, error)
	mockHumanAuthCropFaceVerifyImage                           func(ctx context.Context, request *CropFaceVerifyImageReq, options ...MethodOptionFunc) (*CropFaceVerifyImageResp, *Response, error)
	mockHumanAuthCreateIdentity                                func(ctx context.Context, request *CreateIdentityReq, options ...MethodOptionFunc) (*CreateIdentityResp, *Response, error)
	mockAIRecognizeBasicImage                                  func(ctx context.Context, request *RecognizeBasicImageReq, options ...MethodOptionFunc) (*RecognizeBasicImageResp, *Response, error)
	mockAIRecognizeSpeechStream                                func(ctx context.Context, request *RecognizeSpeechStreamReq, options ...MethodOptionFunc) (*RecognizeSpeechStreamResp, *Response, error)
	mockAIRecognizeSpeechFile                                  func(ctx context.Context, request *RecognizeSpeechFileReq, options ...MethodOptionFunc) (*RecognizeSpeechFileResp, *Response, error)
	mockAITranslateText                                        func(ctx context.Context, request *TranslateTextReq, options ...MethodOptionFunc) (*TranslateTextResp, *Response, error)
	mockAIDetectTextLanguage                                   func(ctx context.Context, request *DetectTextLanguageReq, options ...MethodOptionFunc) (*DetectTextLanguageResp, *Response, error)
	mockAIDetectFaceAttributes                                 func(ctx context.Context, request *DetectFaceAttributesReq, options ...MethodOptionFunc) (*DetectFaceAttributesResp, *Response, error)
	mockAttendanceGetAttendanceGroupList                       func(ctx context.Context, request *GetAttendanceGroupListReq, options ...MethodOptionFunc) (*GetAttendanceGroupListResp, *Response, error)
	mockAttendanceCreateAttendanceGroup                        func(ctx context.Context, request *CreateAttendanceGroupReq, options ...MethodOptionFunc) (*CreateAttendanceGroupResp, *Response, error)
	mockAttendanceSearchAttendanceGroup                        func(ctx context.Context, request *SearchAttendanceGroupReq, options ...MethodOptionFunc) (*SearchAttendanceGroupResp, *Response, error)
	mockAttendanceGetAttendanceGroup                           func(ctx context.Context, request *GetAttendanceGroupReq, options ...MethodOptionFunc) (*GetAttendanceGroupResp, *Response, error)
	mockAttendanceDeleteAttendanceGroup                        func(ctx context.Context, request *DeleteAttendanceGroupReq, options ...MethodOptionFunc) (*DeleteAttendanceGroupResp, *Response, error)
	mockAttendanceGetAttendanceShiftList                       func(ctx context.Context, request *GetAttendanceShiftListReq, options ...MethodOptionFunc) (*GetAttendanceShiftListResp, *Response, error)
	mockAttendanceGetAttendanceShift                           func(ctx context.Context, request *GetAttendanceShiftReq, options ...MethodOptionFunc) (*GetAttendanceShiftResp, *Response, error)
	mockAttendanceGetAttendanceShiftDetail                     func(ctx context.Context, request *GetAttendanceShiftDetailReq, options ...MethodOptionFunc) (*GetAttendanceShiftDetailResp, *Response, error)
	mockAttendanceDeleteAttendanceShift                        func(ctx context.Context, request *DeleteAttendanceShiftReq, options ...MethodOptionFunc) (*DeleteAttendanceShiftResp, *Response, error)
	mockAttendanceCreateAttendanceShift                        func(ctx context.Context, request *CreateAttendanceShiftReq, options ...MethodOptionFunc) (*CreateAttendanceShiftResp, *Response, error)
	mockAttendanceGetAttendanceUserDailyShift                  func(ctx context.Context, request *GetAttendanceUserDailyShiftReq, options ...MethodOptionFunc) (*GetAttendanceUserDailyShiftResp, *Response, error)
	mockAttendanceBatchCreateAttendanceUserDailyShift          func(ctx context.Context, request *BatchCreateAttendanceUserDailyShiftReq, options ...MethodOptionFunc) (*BatchCreateAttendanceUserDailyShiftResp, *Response, error)
	mockAttendanceGetAttendanceUserStatsField                  func(ctx context.Context, request *GetAttendanceUserStatsFieldReq, options ...MethodOptionFunc) (*GetAttendanceUserStatsFieldResp, *Response, error)
	mockAttendanceGetAttendanceUserStatsView                   func(ctx context.Context, request *GetAttendanceUserStatsViewReq, options ...MethodOptionFunc) (*GetAttendanceUserStatsViewResp, *Response, error)
	mockAttendanceUpdateAttendanceUserStatsView                func(ctx context.Context, request *UpdateAttendanceUserStatsViewReq, options ...MethodOptionFunc) (*UpdateAttendanceUserStatsViewResp, *Response, error)
	mockAttendanceGetAttendanceUserStatsData                   func(ctx context.Context, request *GetAttendanceUserStatsDataReq, options ...MethodOptionFunc) (*GetAttendanceUserStatsDataResp, *Response, error)
	mockAttendanceBatchGetAttendanceUserFlow                   func(ctx context.Context, request *BatchGetAttendanceUserFlowReq, options ...MethodOptionFunc) (*BatchGetAttendanceUserFlowResp, *Response, error)
	mockAttendanceGetAttendanceUserFlow                        func(ctx context.Context, request *GetAttendanceUserFlowReq, options ...MethodOptionFunc) (*GetAttendanceUserFlowResp, *Response, error)
	mockAttendanceGetAttendanceUserTask                        func(ctx context.Context, request *GetAttendanceUserTaskReq, options ...MethodOptionFunc) (*GetAttendanceUserTaskResp, *Response, error)
	mockAttendanceBatchCreateAttendanceUserFlow                func(ctx context.Context, request *BatchCreateAttendanceUserFlowReq, options ...MethodOptionFunc) (*BatchCreateAttendanceUserFlowResp, *Response, error)
	mockAttendanceGetAttendanceUserTaskRemedyAllowedRemedyList func(ctx context.Context, request *GetAttendanceUserTaskRemedyAllowedRemedyListReq, options ...MethodOptionFunc) (*GetAttendanceUserTaskRemedyAllowedRemedyListResp, *Response, error)
	mockAttendanceGetAttendanceUserTaskRemedy                  func(ctx context.Context, request *GetAttendanceUserTaskRemedyReq, options ...MethodOptionFunc) (*GetAttendanceUserTaskRemedyResp, *Response, error)
	mockAttendanceCreateAttendanceUserTaskRemedy               func(ctx context.Context, request *CreateAttendanceUserTaskRemedyReq, options ...MethodOptionFunc) (*CreateAttendanceUserTaskRemedyResp, *Response, error)
	mockAttendanceGetAttendanceUserSettingList                 func(ctx context.Context, request *GetAttendanceUserSettingListReq, options ...MethodOptionFunc) (*GetAttendanceUserSettingListResp, *Response, error)
	mockAttendanceUpdateAttendanceUserSetting                  func(ctx context.Context, request *UpdateAttendanceUserSettingReq, options ...MethodOptionFunc) (*UpdateAttendanceUserSettingResp, *Response, error)
	mockAttendanceDownloadAttendanceFile                       func(ctx context.Context, request *DownloadAttendanceFileReq, options ...MethodOptionFunc) (*DownloadAttendanceFileResp, *Response, error)
	mockAttendanceUploadAttendanceFile                         func(ctx context.Context, request *UploadAttendanceFileReq, options ...MethodOptionFunc) (*UploadAttendanceFileResp, *Response, error)
	mockAttendanceGetAttendanceUserApproval                    func(ctx context.Context, request *GetAttendanceUserApprovalReq, options ...MethodOptionFunc) (*GetAttendanceUserApprovalResp, *Response, error)
	mockAttendanceCreateAttendanceUserApproval                 func(ctx context.Context, request *CreateAttendanceUserApprovalReq, options ...MethodOptionFunc) (*CreateAttendanceUserApprovalResp, *Response, error)
	mockAttendanceUpdateAttendanceRemedyApproval               func(ctx context.Context, request *UpdateAttendanceRemedyApprovalReq, options ...MethodOptionFunc) (*UpdateAttendanceRemedyApprovalResp, *Response, error)
	mockFileUploadImage                                        func(ctx context.Context, request *UploadImageReq, options ...MethodOptionFunc) (*UploadImageResp, *Response, error)
	mockFileDownloadImage                                      func(ctx context.Context, request *DownloadImageReq, options ...MethodOptionFunc) (*DownloadImageResp, *Response, error)
	mockFileUploadFile                                         func(ctx context.Context, request *UploadFileReq, options ...MethodOptionFunc) (*UploadFileResp, *Response, error)
	mockFileDownloadFile                                       func(ctx context.Context, request *DownloadFileReq, options ...MethodOptionFunc) (*DownloadFileResp, *Response, error)
	mockOKRGetOKRPeriodList                                    func(ctx context.Context, request *GetOKRPeriodListReq, options ...MethodOptionFunc) (*GetOKRPeriodListResp, *Response, error)
	mockOKRBatchGetOKR                                         func(ctx context.Context, request *BatchGetOKRReq, options ...MethodOptionFunc) (*BatchGetOKRResp, *Response, error)
	mockOKRGetUserOKRList                                      func(ctx context.Context, request *GetUserOKRListReq, options ...MethodOptionFunc) (*GetUserOKRListResp, *Response, error)
	mockEHRGetEHREmployeeList                                  func(ctx context.Context, request *GetEHREmployeeListReq, options ...MethodOptionFunc) (*GetEHREmployeeListResp, *Response, error)
	mockEHRDownloadEHRAttachments                              func(ctx context.Context, request *DownloadEHRAttachmentsReq, options ...MethodOptionFunc) (*DownloadEHRAttachmentsResp, *Response, error)
	mockTenantGetTenant                                        func(ctx context.Context, request *GetTenantReq, options ...MethodOptionFunc) (*GetTenantResp, *Response, error)
	mockSearchCreateSearchDataSourceItem                       func(ctx context.Context, request *CreateSearchDataSourceItemReq, options ...MethodOptionFunc) (*CreateSearchDataSourceItemResp, *Response, error)
	mockSearchGetSearchDataSourceItem                          func(ctx context.Context, request *GetSearchDataSourceItemReq, options ...MethodOptionFunc) (*GetSearchDataSourceItemResp, *Response, error)
	mockSearchDeleteSearchDataSourceItem                       func(ctx context.Context, request *DeleteSearchDataSourceItemReq, options ...MethodOptionFunc) (*DeleteSearchDataSourceItemResp, *Response, error)
	mockSearchCreateSearchDataSource                           func(ctx context.Context, request *CreateSearchDataSourceReq, options ...MethodOptionFunc) (*CreateSearchDataSourceResp, *Response, error)
	mockSearchGetSearchDataSource                              func(ctx context.Context, request *GetSearchDataSourceReq, options ...MethodOptionFunc) (*GetSearchDataSourceResp, *Response, error)
	mockSearchUpdateSearchDataSource                           func(ctx context.Context, request *UpdateSearchDataSourceReq, options ...MethodOptionFunc) (*UpdateSearchDataSourceResp, *Response, error)
	mockSearchGetSearchDataSourceList                          func(ctx context.Context, request *GetSearchDataSourceListReq, options ...MethodOptionFunc) (*GetSearchDataSourceListResp, *Response, error)
	mockSearchDeleteSearchDataSource                           func(ctx context.Context, request *DeleteSearchDataSourceReq, options ...MethodOptionFunc) (*DeleteSearchDataSourceResp, *Response, error)
	mockHireGetHireJob                                         func(ctx context.Context, request *GetHireJobReq, options ...MethodOptionFunc) (*GetHireJobResp, *Response, error)
	mockHireGetHireJobManager                                  func(ctx context.Context, request *GetHireJobManagerReq, options ...MethodOptionFunc) (*GetHireJobManagerResp, *Response, error)
	mockHireGetHireTalent                                      func(ctx context.Context, request *GetHireTalentReq, options ...MethodOptionFunc) (*GetHireTalentResp, *Response, error)
	mockHireGetHireAttachment                                  func(ctx context.Context, request *GetHireAttachmentReq, options ...MethodOptionFunc) (*GetHireAttachmentResp, *Response, error)
	mockHireGetHireAttachmentPreview                           func(ctx context.Context, request *GetHireAttachmentPreviewReq, options ...MethodOptionFunc) (*GetHireAttachmentPreviewResp, *Response, error)
	mockHireGetHireResumeSource                                func(ctx context.Context, request *GetHireResumeSourceReq, options ...MethodOptionFunc) (*GetHireResumeSourceResp, *Response, error)
	mockHireCreateHireNote                                     func(ctx context.Context, request *CreateHireNoteReq, options ...MethodOptionFunc) (*CreateHireNoteResp, *Response, error)
	mockHireUpdateHireNote                                     func(ctx context.Context, request *UpdateHireNoteReq, options ...MethodOptionFunc) (*UpdateHireNoteResp, *Response, error)
	mockHireGetHireNote                                        func(ctx context.Context, request *GetHireNoteReq, options ...MethodOptionFunc) (*GetHireNoteResp, *Response, error)
	mockHireGetHireNoteList                                    func(ctx context.Context, request *GetHireNoteListReq, options ...MethodOptionFunc) (*GetHireNoteListResp, *Response, error)
	mockHireGetHireReferralByApplication                       func(ctx context.Context, request *GetHireReferralByApplicationReq, options ...MethodOptionFunc) (*GetHireReferralByApplicationResp, *Response, error)
	mockHireGetHireJobProcessList                              func(ctx context.Context, request *GetHireJobProcessListReq, options ...MethodOptionFunc) (*GetHireJobProcessListResp, *Response, error)
	mockHireCreateHireApplication                              func(ctx context.Context, request *CreateHireApplicationReq, options ...MethodOptionFunc) (*CreateHireApplicationResp, *Response, error)
	mockHireTerminateHireApplication                           func(ctx context.Context, request *TerminateHireApplicationReq, options ...MethodOptionFunc) (*TerminateHireApplicationResp, *Response, error)
	mockHireGetHireApplication                                 func(ctx context.Context, request *GetHireApplicationReq, options ...MethodOptionFunc) (*GetHireApplicationResp, *Response, error)
	mockHireGetHireApplicationList                             func(ctx context.Context, request *GetHireApplicationListReq, options ...MethodOptionFunc) (*GetHireApplicationListResp, *Response, error)
	mockHireGetHireApplicationInterviewList                    func(ctx context.Context, request *GetHireApplicationInterviewListReq, options ...MethodOptionFunc) (*GetHireApplicationInterviewListResp, *Response, error)
	mockHireGetHireOfferByApplication                          func(ctx context.Context, request *GetHireOfferByApplicationReq, options ...MethodOptionFunc) (*GetHireOfferByApplicationResp, *Response, error)
	mockHireGetHireOfferSchema                                 func(ctx context.Context, request *GetHireOfferSchemaReq, options ...MethodOptionFunc) (*GetHireOfferSchemaResp, *Response, error)
	mockHireMakeHireTransferOnboardByApplication               func(ctx context.Context, request *MakeHireTransferOnboardByApplicationReq, options ...MethodOptionFunc) (*MakeHireTransferOnboardByApplicationResp, *Response, error)
	mockHireUpdateHireEmployee                                 func(ctx context.Context, request *UpdateHireEmployeeReq, options ...MethodOptionFunc) (*UpdateHireEmployeeResp, *Response, error)
	mockHireGetHireEmployeeByApplication                       func(ctx context.Context, request *GetHireEmployeeByApplicationReq, options ...MethodOptionFunc) (*GetHireEmployeeByApplicationResp, *Response, error)
	mockHireGetHireEmployee                                    func(ctx context.Context, request *GetHireEmployeeReq, options ...MethodOptionFunc) (*GetHireEmployeeResp, *Response, error)
	mockTaskCreateTaskCollaborator                             func(ctx context.Context, request *CreateTaskCollaboratorReq, options ...MethodOptionFunc) (*CreateTaskCollaboratorResp, *Response, error)
	mockTaskGetTaskCollaboratorList                            func(ctx context.Context, request *GetTaskCollaboratorListReq, options ...MethodOptionFunc) (*GetTaskCollaboratorListResp, *Response, error)
	mockTaskDeleteTaskCollaborator                             func(ctx context.Context, request *DeleteTaskCollaboratorReq, options ...MethodOptionFunc) (*DeleteTaskCollaboratorResp, *Response, error)
	mockTaskCreateTaskFollower                                 func(ctx context.Context, request *CreateTaskFollowerReq, options ...MethodOptionFunc) (*CreateTaskFollowerResp, *Response, error)
	mockTaskGetTaskFollowerList                                func(ctx context.Context, request *GetTaskFollowerListReq, options ...MethodOptionFunc) (*GetTaskFollowerListResp, *Response, error)
	mockTaskDeleteTaskFollower                                 func(ctx context.Context, request *DeleteTaskFollowerReq, options ...MethodOptionFunc) (*DeleteTaskFollowerResp, *Response, error)
	mockTaskCreateTaskReminder                                 func(ctx context.Context, request *CreateTaskReminderReq, options ...MethodOptionFunc) (*CreateTaskReminderResp, *Response, error)
	mockTaskGetTaskReminderList                                func(ctx context.Context, request *GetTaskReminderListReq, options ...MethodOptionFunc) (*GetTaskReminderListResp, *Response, error)
	mockTaskDeleteTaskReminder                                 func(ctx context.Context, request *DeleteTaskReminderReq, options ...MethodOptionFunc) (*DeleteTaskReminderResp, *Response, error)
	mockTaskCreateTask                                         func(ctx context.Context, request *CreateTaskReq, options ...MethodOptionFunc) (*CreateTaskResp, *Response, error)
	mockTaskGetTask                                            func(ctx context.Context, request *GetTaskReq, options ...MethodOptionFunc) (*GetTaskResp, *Response, error)
	mockTaskGetTaskList                                        func(ctx context.Context, request *GetTaskListReq, options ...MethodOptionFunc) (*GetTaskListResp, *Response, error)
	mockTaskDeleteTask                                         func(ctx context.Context, request *DeleteTaskReq, options ...MethodOptionFunc) (*DeleteTaskResp, *Response, error)
	mockTaskUpdateTask                                         func(ctx context.Context, request *UpdateTaskReq, options ...MethodOptionFunc) (*UpdateTaskResp, *Response, error)
	mockTaskCompleteTask                                       func(ctx context.Context, request *CompleteTaskReq, options ...MethodOptionFunc) (*CompleteTaskResp, *Response, error)
	mockTaskUncompleteTask                                     func(ctx context.Context, request *UncompleteTaskReq, options ...MethodOptionFunc) (*UncompleteTaskResp, *Response, error)
	mockTaskCreateTaskComment                                  func(ctx context.Context, request *CreateTaskCommentReq, options ...MethodOptionFunc) (*CreateTaskCommentResp, *Response, error)
	mockTaskGetTaskComment                                     func(ctx context.Context, request *GetTaskCommentReq, options ...MethodOptionFunc) (*GetTaskCommentResp, *Response, error)
	mockTaskDeleteTaskComment                                  func(ctx context.Context, request *DeleteTaskCommentReq, options ...MethodOptionFunc) (*DeleteTaskCommentResp, *Response, error)
	mockTaskUpdateTaskComment                                  func(ctx context.Context, request *UpdateTaskCommentReq, options ...MethodOptionFunc) (*UpdateTaskCommentResp, *Response, error)
	mockACSGetACSAccessRecordPhoto                             func(ctx context.Context, request *GetACSAccessRecordPhotoReq, options ...MethodOptionFunc) (*GetACSAccessRecordPhotoResp, *Response, error)
	mockACSGetACSAccessRecordList                              func(ctx context.Context, request *GetACSAccessRecordListReq, options ...MethodOptionFunc) (*GetACSAccessRecordListResp, *Response, error)
	mockACSGetACSDeviceList                                    func(ctx context.Context, request *GetACSDeviceListReq, options ...MethodOptionFunc) (*GetACSDeviceListResp, *Response, error)
	mockACSGetACSUserFace                                      func(ctx context.Context, request *GetACSUserFaceReq, options ...MethodOptionFunc) (*GetACSUserFaceResp, *Response, error)
	mockACSUpdateACSUserFace                                   func(ctx context.Context, request *UpdateACSUserFaceReq, options ...MethodOptionFunc) (*UpdateACSUserFaceResp, *Response, error)
	mockACSGetACSUser                                          func(ctx context.Context, request *GetACSUserReq, options ...MethodOptionFunc) (*GetACSUserResp, *Response, error)
	mockACSUpdateACSUser                                       func(ctx context.Context, request *UpdateACSUserReq, options ...MethodOptionFunc) (*UpdateACSUserResp, *Response, error)
	mockACSGetACSUserList                                      func(ctx context.Context, request *GetACSUserListReq, options ...MethodOptionFunc) (*GetACSUserListResp, *Response, error)
	mockBaikeCreateBaikeDraft                                  func(ctx context.Context, request *CreateBaikeDraftReq, options ...MethodOptionFunc) (*CreateBaikeDraftResp, *Response, error)
	mockBaikeCreateBaikeUpdate                                 func(ctx context.Context, request *CreateBaikeUpdateReq, options ...MethodOptionFunc) (*CreateBaikeUpdateResp, *Response, error)
	mockBaikeGetBaikeEntity                                    func(ctx context.Context, request *GetBaikeEntityReq, options ...MethodOptionFunc) (*GetBaikeEntityResp, *Response, error)
	mockBaikeGetBaikeEntityList                                func(ctx context.Context, request *GetBaikeEntityListReq, options ...MethodOptionFunc) (*GetBaikeEntityListResp, *Response, error)
	mockBaikeMatchBaikeEntity                                  func(ctx context.Context, request *MatchBaikeEntityReq, options ...MethodOptionFunc) (*MatchBaikeEntityResp, *Response, error)
	mockBaikeSearchBaikeEntity                                 func(ctx context.Context, request *SearchBaikeEntityReq, options ...MethodOptionFunc) (*SearchBaikeEntityResp, *Response, error)
	mockBaikeHighlightBaikeEntity                              func(ctx context.Context, request *HighlightBaikeEntityReq, options ...MethodOptionFunc) (*HighlightBaikeEntityResp, *Response, error)
	mockPassportGetPassportSession                             func(ctx context.Context, request *GetPassportSessionReq, options ...MethodOptionFunc) (*GetPassportSessionResp, *Response, error)
	mockEventGetEventOutboundIpList                            func(ctx context.Context, request *GetEventOutboundIpListReq, options ...MethodOptionFunc) (*GetEventOutboundIpListResp, *Response, error)
}

// Mock return mock client
func (r *Lark) Mock() *Mock {
	return r.mock
}
