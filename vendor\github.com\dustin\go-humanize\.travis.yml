sudo: false
language: go
go_import_path: github.com/dustin/go-humanize
go:
  - 1.13.x
  - 1.14.x
  - 1.15.x
  - 1.16.x
  - stable
  - master
matrix:
  allow_failures:
    - go: master
  fast_finish: true
install:
  - # Do nothing. This is needed to prevent default install action "go get -t -v ./..." from happening here (we want it to happen inside script step).
script:
  - diff -u <(echo -n) <(gofmt -d -s .)
  - go vet .
  - go install -v -race ./...
  - go test -v -race ./...
