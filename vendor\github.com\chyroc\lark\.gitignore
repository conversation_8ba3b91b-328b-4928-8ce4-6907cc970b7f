# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

.idea/
ttt
coverage.txt
coverage.tmp
debug.go
.readme
node_modules/
*/.vuepress/dist/
dist/
venv/
.github/internal/go.sum
.github/all-api.json
generate
test/debug_test.go
