# unifying the coding style for different editors and IDEs => editorconfig.org

; indicate this is the root of the project
root = true

###########################################################
; common
###########################################################

[*]
charset = utf-8

end_of_line = LF
insert_final_newline = true
trim_trailing_whitespace = true

indent_style = space
indent_size = 2

###########################################################
; make
###########################################################

[Makefile]
indent_style = tab

[makefile]
indent_style = tab

###########################################################
; markdown
###########################################################

[*.md]
trim_trailing_whitespace = false

###########################################################
; golang
###########################################################

[*.go]
indent_style = tab