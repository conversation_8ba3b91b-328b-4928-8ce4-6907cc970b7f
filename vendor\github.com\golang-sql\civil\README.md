# Civil Date and Time

[![GoDoc](https://godoc.org/github.com/golang-sql/civil?status.svg)](https://godoc.org/github.com/golang-sql/civil)

Civil provides Date, Time of Day, and DateTime data types.

While there are many uses, using specific types when working
with databases make is conceptually eaiser to understand what value
is set in the remote system.

## Source

This civil package was extracted and forked from `cloud.google.com/go/civil`.
As such the license and contributing requirements remain the same as that
module.
