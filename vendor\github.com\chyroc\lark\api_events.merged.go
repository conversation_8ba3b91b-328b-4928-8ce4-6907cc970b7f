// Code generated by lark_sdk_gen. DO NOT EDIT.
/**
 * Copyright 2022 chyroc
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package lark

import (
	"context"
)

// EventV1AddBot
//
// 为了更好地提升该事件的安全性，我们对其进行了升级，请尽快迁移至[新版本>>](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-bot/events/added)
// 机器人被邀请加入群聊时触发此事件。
// - 依赖条件：应用必须开启了[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uMTNxYjLzUTM24yM1EjN
func (r *EventCallbackService) HandlerEventV1AddBot(f EventV1AddBotHandler) {
	r.cli.eventHandler.eventV1AddBotHandler = f
}

// EventV1AddBotHandler event EventV1AddBot handler
type EventV1AddBotHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1AddBot) (string, error)

// EventV1AddBot ...
type EventV1AddBot struct {
	AppID               string                           `json:"app_id,omitempty"`                 // 如: cli_9c8609450f78d102
	ChatI18nNames       *EventV1AddBotEventChatI18nNames `json:"chat_i18n_names,omitempty"`        // 群名称国际化字段
	ChatName            string                           `json:"chat_name,omitempty"`              // 如: 群名称
	ChatOwnerEmployeeID string                           `json:"chat_owner_employee_id,omitempty"` // 群主的employee_id（即“用户ID”。如果群主是机器人则没有这个字段，仅企业自建应用返回）. 如: ca51d83b
	ChatOwnerName       string                           `json:"chat_owner_name,omitempty"`        // 群主姓名. 如: xxx
	ChatOwnerOpenID     string                           `json:"chat_owner_open_id,omitempty"`     // 群主的open_id. 如: ou_18eac85d35a26f989317ad4f02e8bbbb
	OpenChatID          string                           `json:"open_chat_id,omitempty"`           // 群聊的id. 如: oc_e520983d3e4f5ec7306069bffe672aa3
	OperatorEmployeeID  string                           `json:"operator_employee_id,omitempty"`   // 操作者的emplolyee_id ，仅企业自建应用返回. 如: ca51d83b
	OperatorName        string                           `json:"operator_name,omitempty"`          // 操作者姓名. 如: yyy
	OperatorOpenID      string                           `json:"operator_open_id,omitempty"`       // 操作者的open_id. 如: ou_18eac85d35a26f989317ad4f02e8bbbb
	OwnerIsBot          bool                             `json:"owner_is_bot,omitempty"`           // 群主是否是机器人. 如: false
	TenantKey           string                           `json:"tenant_key,omitempty"`             // 企业标识. 如: 736588c9260f175d
	Type                string                           `json:"type,omitempty"`                   // 事件类型. 如: add_bot
}

// EventV1AddBotEventChatI18nNames ...
type EventV1AddBotEventChatI18nNames struct {
	EnUs string `json:"en_us,omitempty"` // 如: 英文标题
	ZhCn string `json:"zh_cn,omitempty"` // 如: 中文标题
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1AddUserToChat
//
// 如果你希望订阅机器人进出群、群内有人@机器人事件，请前往[机器人进群](https://open.feishu.cn/document/ukTMukTMukTM/ugzMugzMugzM/event/bot-added-to-group) 或 [机器人退群](https://open.feishu.cn/document/ukTMukTMukTM/ucDO04yN4QjL3gDN)。
// 为了更好地提升该事件的安全性，我们对其进行了升级，请尽快迁移至
// [新版本（用户进群）](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-user/events/added)
// 或[新版本（用户出群）>>](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-user/events/deleted)
// 用户进群、出群后触发此事件。
// * 特殊说明：只有开启机器人能力并且机器人所在的群发生上述变化时才能触发此事件。
// 事件包括三个类型：
// 1. 用户进群 - add_user_to_chat
// 2. 用户出群 - remove_user_from_chat
// 3. 撤销加人 - revoke_add_user_from_chat
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uQDOwUjL0gDM14CN4ATN/event/user-joins-or-leave-group
func (r *EventCallbackService) HandlerEventV1AddUserToChat(f EventV1AddUserToChatHandler) {
	r.cli.eventHandler.eventV1AddUserToChatHandler = f
}

// EventV1AddUserToChatHandler event EventV1AddUserToChat handler
type EventV1AddUserToChatHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1AddUserToChat) (string, error)

// EventV1AddUserToChat ...
type EventV1AddUserToChat struct {
	AppID     string                             `json:"app_id,omitempty"`     // 如: cli_9c8609450f78d102
	ChatID    string                             `json:"chat_id,omitempty"`    // 群聊的id. 如: oc_9e9619b938c9571c1c3165681cdaead5
	Operator  *EventV1AddUserToChatEventOperator `json:"operator,omitempty"`   // 用户进出群的操作人。用户主动退群的话，operator 就是user自己
	TenantKey string                             `json:"tenant_key,omitempty"` // 如: 736588c9260f175d
	Type      string                             `json:"type,omitempty"`       // 事件类型，add_user_to_chat/remove_user_from_chat/revoke_add_user_from_chat. 如: add_user_to_chat
	Users     []*EventV1AddUserToChatEventUser   `json:"users,omitempty"`
}

// EventV1AddUserToChatEventOperator ...
type EventV1AddUserToChatEventOperator struct {
	OpenID string `json:"open_id,omitempty"` // 员工对此应用的唯一标识，同一员工对不同应用的open_id不同. 如: ou_18eac85d35a26f989317ad4f02e8bbbb
	UserID string `json:"user_id,omitempty"` // 即“用户ID”，仅企业自建应用会返回. 如: ca51d83b
}

// EventV1AddUserToChatEventUser ...
type EventV1AddUserToChatEventUser struct {
	Name   string `json:"name,omitempty"`    // 如: James
	OpenID string `json:"open_id,omitempty"` // 如: ou_706adeb944ab1473b9fb3e7da2a40b68
	UserID string `json:"user_id,omitempty"` // 如: 51g97a4g
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1AppOpen
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 当租户第一次安装并启用此应用时触发此事件，启用应用包含以下场景：
// - 当租户管理员后台首次开通应用
// - 租户内的普通成员首次安装此应用
// 只有应用商店应用才能订阅此事件。自建应用无此事件。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/event/app-first-enabled
func (r *EventCallbackService) HandlerEventV1AppOpen(f EventV1AppOpenHandler) {
	r.cli.eventHandler.eventV1AppOpenHandler = f
}

// EventV1AppOpenHandler event EventV1AppOpen handler
type EventV1AppOpenHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1AppOpen) (string, error)

// EventV1AppOpen ...
type EventV1AppOpen struct {
	AppID             string                                `json:"app_id,omitempty"`             // 开通的应用ID. 如: cli_xxx
	TenantKey         string                                `json:"tenant_key,omitempty"`         // 开通应用的企业唯一标识. 如: xxx
	Type              string                                `json:"type,omitempty"`               // 事件类型. 如: app_open
	Applicants        []*EventV1AppOpenEventApplicant       `json:"applicants,omitempty"`         // 应用的申请者，可能有多个
	Installer         *EventV1AppOpenEventInstaller         `json:"installer,omitempty"`          // 当应用被管理员安装时，返回此字段。如果是自动安装或由普通成员获取时，没有此字段
	InstallerEmployee *EventV1AppOpenEventInstallerEmployee `json:"installer_employee,omitempty"` // 当应用被普通成员安装时，返回此字段
}

// EventV1AppOpenEventApplicant ...
type EventV1AppOpenEventApplicant struct {
	OpenID string `json:"open_id,omitempty"` // 用户对此应用的唯一标识，同一用户对不同应用的open_id不同. 如: xxx
}

// EventV1AppOpenEventInstaller ...
type EventV1AppOpenEventInstaller struct {
	OpenID string `json:"open_id,omitempty"` // 用户对此应用的唯一标识，同一用户对不同应用的open_id不同. 如: xxx
}

// EventV1AppOpenEventInstallerEmployee ...
type EventV1AppOpenEventInstallerEmployee struct {
	OpenID string `json:"open_id,omitempty"` // 用户对此应用的唯一标识，同一用户对不同应用的open_id不同. 如: xxx
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1AppStatusChange
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 当企业管理员在管理员后台启用、停用应用，或应用被平台停用时，开放平台推送 app_status_change 事件到请求网址。
// - 订阅条件：只有应用商店应用才能订阅此事件。自建应用无此事件。
// - 特殊说明：管理员手动安装应用时，也会触发启用事件。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/event/app-enabled-or-disabled
func (r *EventCallbackService) HandlerEventV1AppStatusChange(f EventV1AppStatusChangeHandler) {
	r.cli.eventHandler.eventV1AppStatusChangeHandler = f
}

// EventV1AppStatusChangeHandler event EventV1AppStatusChange handler
type EventV1AppStatusChangeHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1AppStatusChange) (string, error)

// EventV1AppStatusChange ...
type EventV1AppStatusChange struct {
	AppID     string                               `json:"app_id,omitempty"`     // 应用ID. 如: cli_xxx
	TenantKey string                               `json:"tenant_key,omitempty"` // 企业唯一标识. 如: xxx
	Type      string                               `json:"type,omitempty"`       // 事件类型. 如: app_status_change
	Status    string                               `json:"status,omitempty"`     // 应用状态 start_by_tenant: 租户启用; stop_by_tenant: 租户停用; stop_by_platform: 平台停用. 如: start_by_tenant
	Operator  *EventV1AppStatusChangeEventOperator `json:"operator,omitempty"`   // 仅status=start_by_tenant时有此字段
}

// EventV1AppStatusChangeEventOperator ...
type EventV1AppStatusChangeEventOperator struct {
	OpenID  string `json:"open_id,omitempty"`  // 如: xxx
	UserID  string `json:"user_id,omitempty"`  // 仅自建应用才会返回. 如: yyy
	UnionID string `json:"union_id,omitempty"` // 用户在ISV下的唯一标识. 如: zzz
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1AppTicket
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 对于应用商店应用，开放平台会每隔1小时推送一次 app_ticket ，应用通过该 app_ticket 获取 app_access_token。
// - 特殊说明： 应用商店应用自动订阅此事件；企业自建应用不需要此事件。
// - 搭配使用：[触发app_ticket重新推送
// ](https://open.feishu.cn/document/ukTMukTMukTM/ukDNz4SO0MjL5QzM/auth-v3/auth/app_ticket_resend)、[通过app_ticket获取token
// ](https://open.feishu.cn/document/ukTMukTMukTM/ukDNz4SO0MjL5QzM/auth-v3/auth/app_access_token)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/event/app_ticket-events
func (r *EventCallbackService) HandlerEventV1AppTicket(f EventV1AppTicketHandler) {
	r.cli.eventHandler.eventV1AppTicketHandler = f
}

// EventV1AppTicketHandler event EventV1AppTicket handler
type EventV1AppTicketHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1AppTicket) (string, error)

// EventV1AppTicket ...
type EventV1AppTicket struct {
	AppID     string `json:"app_id,omitempty"`     // 如: cli_xxx
	AppTicket string `json:"app_ticket,omitempty"` // 如: xxx
	Type      string `json:"type,omitempty"`       // 如: app_ticket
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1AppUninstalled
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// - 自建应用无此事件。
// - 企业解散后会推送此事件。商店应用开发者可在收到此事件后进行相应的账户注销、数据清理等处理。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/event/app-uninstalled
func (r *EventCallbackService) HandlerEventV1AppUninstalled(f EventV1AppUninstalledHandler) {
	r.cli.eventHandler.eventV1AppUninstalledHandler = f
}

// EventV1AppUninstalledHandler event EventV1AppUninstalled handler
type EventV1AppUninstalledHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1AppUninstalled) (string, error)

// EventV1AppUninstalled ...
type EventV1AppUninstalled struct {
	AppID     string `json:"app_id,omitempty"`     // 被卸载的应用ID. 如: cli_xxx
	TenantKey string `json:"tenant_key,omitempty"` // 卸载应用的企业ID. 如: xxx
	Type      string `json:"type,omitempty"`       // 事件类型. 如: app_uninstalled
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1ApprovalCc
//
//
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/ugDNyUjL4QjM14CO0ITN
func (r *EventCallbackService) HandlerEventV1ApprovalCc(f EventV1ApprovalCcHandler) {
	r.cli.eventHandler.eventV1ApprovalCcHandler = f
}

// EventV1ApprovalCcHandler event EventV1ApprovalCc handler
type EventV1ApprovalCcHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1ApprovalCc) (string, error)

// EventV1ApprovalCc ...
type EventV1ApprovalCc struct {
	AppID        string `json:"app_id,omitempty"` // 如: cli_xxx
	TenantKey    string `json:"tenant_key,omitempty"`
	Type         string `json:"type,omitempty"`          // 固定 approval_cc
	ApprovalCode string `json:"approval_code,omitempty"` // 审批定义 Code
	InstanceCode string `json:"instance_code,omitempty"` // 审批实例 Code
	ID           string `json:"id,omitempty"`            // 抄送 ID
	UserID       string `json:"user_id,omitempty"`       // 被抄送人
	CreateTime   int64  `json:"create_time,omitempty"`   // 抄送时间
	Operate      string `json:"operate,omitempty"`       // 被抄送人
	Status       string `json:"status,omitempty"`        // 操作类型, CREATE: 抄送, REVOKE: 撤回
	From         string `json:"from,omitempty"`          // 抄送人, 可能为空
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1ApprovalTask
//
//
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/ugDNyUjL4QjM14CO0ITN
func (r *EventCallbackService) HandlerEventV1ApprovalTask(f EventV1ApprovalTaskHandler) {
	r.cli.eventHandler.eventV1ApprovalTaskHandler = f
}

// EventV1ApprovalTaskHandler event EventV1ApprovalTask handler
type EventV1ApprovalTaskHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1ApprovalTask) (string, error)

// EventV1ApprovalTask ...
type EventV1ApprovalTask struct {
	AppID        string `json:"app_id,omitempty"`  // 如: cli_xxx
	OpenID       string `json:"open_id,omitempty"` // 如: xxx
	TenantKey    string `json:"tenant_key,omitempty"`
	Type         string `json:"type,omitempty"`          // 固定 approval_task
	ApprovalCode string `json:"approval_code,omitempty"` // 审批定义 Code
	InstanceCode string `json:"instance_code,omitempty"` // 审批实例 Code
	TaskID       string `json:"task_id,omitempty"`       // 审批任务 ID
	UserID       string `json:"user_id,omitempty"`       // 操作人 ID（当 task 为自动通过类型时，user_id 为空）
	Status       string `json:"status,omitempty"`        // 任务状态, REVERTED - 已还原, PENDING - 进行中, APPROVED - 已通过, REJECTED - 已拒绝, TRANSFERRED - 已转交, DONE - 已完成
	OperateTime  string `json:"operate_time,omitempty"`  // 事件发生时间
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1ChatDisband
//
// 如果你希望订阅机器人进出群、群内有人@机器人事件，请前往[机器人进群](https://open.feishu.cn/document/ukTMukTMukTM/ugzMugzMugzM/event/bot-added-to-group) 或 [机器人退群](https://open.feishu.cn/document/ukTMukTMukTM/ucDO04yN4QjL3gDN)。
// 为了更好地提升该事件的安全性，我们对其进行了升级，请尽快迁移至[新版本>>](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat/events/disbanded)
// 群聊被解散后触发此事件。
// * 特殊说明：只有开启机器人能力并且机器人所在的群被解散时才能触发此事件。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uQDOwUjL0gDM14CN4ATN/event/group-closed
func (r *EventCallbackService) HandlerEventV1ChatDisband(f EventV1ChatDisbandHandler) {
	r.cli.eventHandler.eventV1ChatDisbandHandler = f
}

// EventV1ChatDisbandHandler event EventV1ChatDisband handler
type EventV1ChatDisbandHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1ChatDisband) (string, error)

// EventV1ChatDisband ...
type EventV1ChatDisband struct {
	AppID     string                           `json:"app_id,omitempty"`  // 如: cli_9c8609450f78d102
	ChatID    string                           `json:"chat_id,omitempty"` // 如: oc_9f2df3c095c9395334bb6e70ced0fa83
	Operator  *EventV1ChatDisbandEventOperator `json:"operator,omitempty"`
	TenantKey string                           `json:"tenant_key,omitempty"` // 如: 736588c9260f175d
	Type      string                           `json:"type,omitempty"`       // 如: chat_disband
}

// EventV1ChatDisbandEventOperator ...
type EventV1ChatDisbandEventOperator struct {
	OpenID string `json:"open_id,omitempty"` // 如: ou_18eac85d35a26f989317ad4f02e8bbbb
	UserID string `json:"user_id,omitempty"` // 如: ca51d83b
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1LeaveApprovalV2
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 「审批」应用的表单里如果包含 [请假控件组]，则在此表单审批通过后触发此事件。
// * 特殊说明：如果你订阅了此事件，会收到2条消息，其 [type] 分别为 [leave_approval]、 [leave_approvalV2] 。这两个事件的 [uuid] 不同、 [instance_code] 相同。
// * 依赖权限：[访问审批应用]
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uIDO24iM4YjLygjN/event/leave
func (r *EventCallbackService) HandlerEventV1LeaveApprovalV2(f EventV1LeaveApprovalV2Handler) {
	r.cli.eventHandler.eventV1LeaveApprovalV2Handler = f
}

// EventV1LeaveApprovalV2Handler event EventV1LeaveApprovalV2 handler
type EventV1LeaveApprovalV2Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1LeaveApprovalV2) (string, error)

// EventV1LeaveApprovalV2 ...
type EventV1LeaveApprovalV2 struct {
	AppID          string                                     `json:"app_id,omitempty"`           // 如: cli_xxx
	TenantKey      string                                     `json:"tenant_key,omitempty"`       // 如: xxx
	Type           string                                     `json:"type,omitempty"`             // 如: leave_approvalV2
	InstanceCode   string                                     `json:"instance_code,omitempty"`    // 审批实例Code. 如: xxx
	UserID         string                                     `json:"user_id,omitempty"`          // 用户id. 如: xxx
	OpenID         string                                     `json:"open_id,omitempty"`          // 用户open_id. 如: ou_xxx
	StartTime      int64                                      `json:"start_time,omitempty"`       // 审批发起时间. 如: 1564590532
	EndTime        int64                                      `json:"end_time,omitempty"`         // 审批结束时间. 如: 1564590532
	LeaveName      string                                     `json:"leave_name,omitempty"`       // 假期名称. 如: @i18n@123456
	LeaveUnit      string                                     `json:"leave_unit,omitempty"`       // 请假最小时长. 如: DAY
	LeaveStartTime string                                     `json:"leave_start_time,omitempty"` // 请假开始时间. 如: 2019-10-01 00:00:00
	LeaveEndTime   string                                     `json:"leave_end_time,omitempty"`   // 请假结束时间. 如: 2019-10-02 00:00:00
	LeaveDetail    []string                                   `json:"leave_detail,omitempty"`     // 具体的请假明细时间
	LeaveRange     []string                                   `json:"leave_range,omitempty"`      // 具体的请假时间范围
	LeaveInterval  int64                                      `json:"leave_interval,omitempty"`   // 请假时长，单位（秒）. 如: 86400
	LeaveReason    string                                     `json:"leave_reason,omitempty"`     // 请假事由. 如: abc
	I18nResources  []*EventV1LeaveApprovalV2EventI18nResource `json:"i18n_resources,omitempty"`   // 国际化文案
}

// EventV1LeaveApprovalV2EventI18nResource ...
type EventV1LeaveApprovalV2EventI18nResource struct {
	Locale    string            `json:"locale,omitempty"`     // 如: en_us
	IsDefault bool              `json:"is_default,omitempty"` // 如: true
	Texts     map[string]string `json:"texts,omitempty"`
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1OrderPaid
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 用户购买应用商店付费应用成功后发送给应用ISV的通知事件。
// - 订阅条件：只有应用商店应用才能订阅此事件。自建应用无此事件。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/event/public-app-purchase
func (r *EventCallbackService) HandlerEventV1OrderPaid(f EventV1OrderPaidHandler) {
	r.cli.eventHandler.eventV1OrderPaidHandler = f
}

// EventV1OrderPaidHandler event EventV1OrderPaid handler
type EventV1OrderPaidHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1OrderPaid) (string, error)

// EventV1OrderPaid ...
type EventV1OrderPaid struct {
	Type          string `json:"type,omitempty"`            // 事件类型. 如: order_paid
	AppID         string `json:"app_id,omitempty"`          // 应用ID. 如: cli_9daeceab98721136
	OrderID       string `json:"order_id,omitempty"`        // 用户购买付费方案时对订单ID 可作为唯一标识. 如: 6704894492631105539
	PricePlanID   string `json:"price_plan_id,omitempty"`   // 付费方案ID. 如: price_9d86fa1333b8110c
	PricePlanType string `json:"price_plan_type,omitempty"` // 用户购买方案类型 "trial" -试用；"permanent"-免费；"per_year"-企业年付费；"per_month"-企业月付费；"per_seat_per_year"-按人按年付费；"per_seat_per_month"-按人按月付费；"permanent_count"-按次付费. 如: per_seat_per_month
	Seats         int64  `json:"seats,omitempty"`           // 表示购买了多少人份. 如: 20
	BuyCount      int64  `json:"buy_count,omitempty"`       // 套餐购买数量 目前都为1. 如: 1
	CreateTime    string `json:"create_time,omitempty"`     // 如: 1502199207
	PayTime       string `json:"pay_time,omitempty"`        // 如: 1502199209
	BuyType       string `json:"buy_type,omitempty"`        // 购买类型 buy普通购买 upgrade为升级购买 renew为续费购买. 如: buy
	SrcOrderID    string `json:"src_order_id,omitempty"`    // 当前为升级购买时(buy_type 为upgrade)，该字段表示原订单ID，升级后原订单失效，状态变为已升级(业务方需要处理). 如: 6704894492631105539
	OrderPayPrice int64  `json:"order_pay_price,omitempty"` // 订单支付价格 单位分，. 如: 10000
	TenantKey     string `json:"tenant_key,omitempty"`      // 购买应用的企业标示. 如: 2f98c01bc23f6847
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1OutApproval
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 「审批」应用的表单里如果包含 [外出控件组]，则在此表单审批通过后触发此事件。
// * 依赖权限：[访问审批应用]
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uIDO24iM4YjLygjN/event/out-of-office
func (r *EventCallbackService) HandlerEventV1OutApproval(f EventV1OutApprovalHandler) {
	r.cli.eventHandler.eventV1OutApprovalHandler = f
}

// EventV1OutApprovalHandler event EventV1OutApproval handler
type EventV1OutApprovalHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1OutApproval) (string, error)

// EventV1OutApproval ...
type EventV1OutApproval struct {
	AppID         string                                 `json:"app_id,omitempty"` // 如: cli_9e28cb7ba56a100e
	I18nResources []*EventV1OutApprovalEventI18nResource `json:"i18n_resources,omitempty"`
	InstanceCode  string                                 `json:"instance_code,omitempty"` // 此审批的唯一标识. 如: 59558CEE-CEF4-45C9-A2C3-DCBF8BEC7341
	OutImage      string                                 `json:"out_image,omitempty"`
	OutInterval   int64                                  `json:"out_interval,omitempty"`   // 外出时长，单位秒. 如: 10800
	OutName       string                                 `json:"out_name,omitempty"`       // 通过i18n_resources里的信息换取相应语言的文案. 如: @i18n@someKey
	OutReason     string                                 `json:"out_reason,omitempty"`     // 如: 外出事由
	OutStartTime  string                                 `json:"out_start_time,omitempty"` // 如: 2020-05-15 15:00:00
	OutEndTime    string                                 `json:"out_end_time,omitempty"`   // 如: 2020-05-15 18:00:00
	OutUnit       string                                 `json:"out_unit,omitempty"`       // 外出时长的单位，HOUR 小时，DAY 天，HALF_DAY 半天. 如: HOUR
	StartTime     int64                                  `json:"start_time,omitempty"`     // 审批开始时间. 如: 1589527346
	EndTime       int64                                  `json:"end_time,omitempty"`       // 审批结束时间. 如: 1589527354
	TenantKey     string                                 `json:"tenant_key,omitempty"`     // 企业唯一标识. 如: 2d520d3b434f175e
	Type          string                                 `json:"type,omitempty"`           // 事件类型. 如: out_approval
	OpenID        string                                 `json:"open_id,omitempty"`        // 申请发起人open_id. 如: ou_xxx
	UserID        string                                 `json:"user_id,omitempty"`        // 申请发起人. 如: g6964gd3
}

// EventV1OutApprovalEventI18nResource ...
type EventV1OutApprovalEventI18nResource struct {
	IsDefault bool              `json:"is_default,omitempty"` // 如: true
	Locale    string            `json:"locale,omitempty"`     // 如: zh_cn
	Texts     map[string]string `json:"texts,omitempty"`
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1P2PChatCreate
//
// 首次会话是用户了解应用的重要机会，你可以发送操作说明、配置地址来指导用户开始使用你的应用。
// 如果是应用商店应用，请务必确保订阅并响应此事件。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uYDNxYjL2QTM24iN0EjN/bot-events
func (r *EventCallbackService) HandlerEventV1P2PChatCreate(f EventV1P2PChatCreateHandler) {
	r.cli.eventHandler.eventV1P2PChatCreateHandler = f
}

// EventV1P2PChatCreateHandler event EventV1P2PChatCreate handler
type EventV1P2PChatCreateHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1P2PChatCreate) (string, error)

// EventV1P2PChatCreate ...
type EventV1P2PChatCreate struct {
	AppID     string                             `json:"app_id,omitempty"`     // 如: cli_9c8609450f78d102
	ChatID    string                             `json:"chat_id,omitempty"`    // 机器人和用户的会话id. 如: oc_26b66a5eb603162b849f91bcd8815b20
	Operator  *EventV1P2PChatCreateEventOperator `json:"operator,omitempty"`   // 会话的发起人。可能是用户，也可能是机器人。
	TenantKey string                             `json:"tenant_key,omitempty"` // 企业标识. 如: 736588c9260f175c
	Type      string                             `json:"type,omitempty"`       // 事件类型. 如: p2p_chat_create
	User      *EventV1P2PChatCreateEventUser     `json:"user,omitempty"`       // 会话的用户
}

// EventV1P2PChatCreateEventOperator ...
type EventV1P2PChatCreateEventOperator struct {
	OpenID string `json:"open_id,omitempty"` // 员工对此应用的唯一标识，同一员工对不同应用的open_id不同. 如: ou_2d2c0399b53d06fd195bb393cd1e38f2
	UserID string `json:"user_id,omitempty"` // 即“用户ID”，仅企业自建应用会返回. 如: gfa21d92
}

// EventV1P2PChatCreateEventUser ...
type EventV1P2PChatCreateEventUser struct {
	Name   string `json:"name,omitempty"`    // 如: 张三
	OpenID string `json:"open_id,omitempty"` // 员工对此应用的唯一标识，同一员工对不同应用的open_id不同. 如: ou_7dede290d6a27698b969a7fd70ca53da
	UserID string `json:"user_id,omitempty"` // 即“用户ID”，仅企业自建应用会返回. 如: gfa21d92
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1ReceiveMessage
//
// ## 接收消息
// 当用户发送消息给机器人或在群聊中@机器人时触发此事件。
// - 依赖权限：[获取用户发给机器人的私聊消息] 、 [获取群聊中用户 @ 机器人的消息]。开启了相应的权限才能获取到相应的消息。
// - 其他条件：应用必须开启了[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)。
// ### 文本消息
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/ugzMugzMugzM/event/receive-message
func (r *EventCallbackService) HandlerEventV1ReceiveMessage(f EventV1ReceiveMessageHandler) {
	r.cli.eventHandler.eventV1ReceiveMessageHandler = f
}

// EventV1ReceiveMessageHandler event EventV1ReceiveMessage handler
type EventV1ReceiveMessageHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1ReceiveMessage) (string, error)

// EventV1ReceiveMessage ...
type EventV1ReceiveMessage struct {
	Type             string   `json:"type,omitempty"`            // 事件类型. 如: message
	AppID            string   `json:"app_id,omitempty"`          // 如: cli_xxx
	TenantKey        string   `json:"tenant_key,omitempty"`      // 企业标识. 如: xxx
	RootID           string   `json:"root_id,omitempty"`         // 如:
	ParentID         string   `json:"parent_id,omitempty"`       // 如:
	OpenChatID       string   `json:"open_chat_id,omitempty"`    // 如: oc_5ce6d572455d361153b7cb51da133945
	ChatType         ChatType `json:"chat_type,omitempty"`       // 私聊private，群聊group. 如: private
	MsgType          MsgType  `json:"msg_type,omitempty"`        // 消息类型. 如: text
	OpenID           string   `json:"open_id,omitempty"`         // 如: ou_18eac85d35a26f989317ad4f02e8bbbb
	EmployeeID       string   `json:"employee_id,omitempty"`     // 即“用户ID”，仅企业自建应用会返回. 如: xxx
	UnionID          string   `json:"union_id,omitempty"`        // 如: xxx
	OpenMessageID    string   `json:"open_message_id,omitempty"` // 如: om_36686ee62209da697d8775375d0c8e88
	IsMention        bool     `json:"is_mention,omitempty"`
	Text             string   `json:"text,omitempty"`                // 消息文本，可能包含被@的人/机器人。如: <at open_id="xxx">@小助手</at> 消息内容 <at open_id="yyy">@张三</at>
	TextWithoutAtBot string   `json:"text_without_at_bot,omitempty"` // 消息内容，会过滤掉at你的机器人的内容. 如: 消息内容 <at open_id="yyy">@张三</at>
	Title            string   `json:"title,omitempty"`               // 富文本标题
	ImageKeys        []string `json:"image_keys,omitempty"`          // 富文本里面的图片的keys
	ImageKey         string   `json:"image_key,omitempty"`           // 图片内容
	FileKey          string   `json:"file_key,omitempty"`            // 文件内容
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1RemedyApproval
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 补卡申请审批通过后触发此事件。 你可以在「打卡」应用里提交补卡申请。
// * 依赖权限：[访问审批应用]
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uIDO24iM4YjLygjN/event/attendance-record-correction
func (r *EventCallbackService) HandlerEventV1RemedyApproval(f EventV1RemedyApprovalHandler) {
	r.cli.eventHandler.eventV1RemedyApprovalHandler = f
}

// EventV1RemedyApprovalHandler event EventV1RemedyApproval handler
type EventV1RemedyApprovalHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1RemedyApproval) (string, error)

// EventV1RemedyApproval ...
type EventV1RemedyApproval struct {
	AppID        string `json:"app_id,omitempty"`        // 如: cli_xxx
	TenantKey    string `json:"tenant_key,omitempty"`    // 如: xxx
	Type         string `json:"type,omitempty"`          // 或者remedy_approval_v2. 如: remedy_approval
	InstanceCode string `json:"instance_code,omitempty"` // 审批实例Code. 如: xxx
	EmployeeID   string `json:"employee_id,omitempty"`   // 用户id. 如: xxx
	OpenID       string `json:"open_id,omitempty"`       // 用户open_id. 如: ou_xxx
	StartTime    int64  `json:"start_time,omitempty"`    // 审批发起时间. 如: 1502199207
	EndTime      int64  `json:"end_time,omitempty"`      // 审批结束时间. 如: 1502199307
	RemedyTime   string `json:"remedy_time,omitempty"`   // 补卡时间. 如: 2018-12-01 12:00:00
	RemedyReason string `json:"remedy_reason,omitempty"` // 补卡原因. 如: xxx
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1RemoveBot
//
// 为了更好地提升该事件的安全性，我们对其进行了升级，请尽快迁移至[新版本>>](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-bot/events/deleted)
// 机器人被从群聊中移除时触发此事件。
// - 依赖条件：应用必须开启了[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/ugzMugzMugzM/event/bot-removed-from-group
func (r *EventCallbackService) HandlerEventV1RemoveBot(f EventV1RemoveBotHandler) {
	r.cli.eventHandler.eventV1RemoveBotHandler = f
}

// EventV1RemoveBotHandler event EventV1RemoveBot handler
type EventV1RemoveBotHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1RemoveBot) (string, error)

// EventV1RemoveBot ...
type EventV1RemoveBot struct {
	AppID               string                              `json:"app_id,omitempty"`                 // 如: cli_9c8609450f78d102
	ChatI18nNames       *EventV1RemoveBotEventChatI18nNames `json:"chat_i18n_names,omitempty"`        // 群名称国际化字段
	ChatName            string                              `json:"chat_name,omitempty"`              // 如: 群名称
	ChatOwnerEmployeeID string                              `json:"chat_owner_employee_id,omitempty"` // 群主的employee_id（即“用户ID”。如果群主是机器人则没有这个字段，仅企业自建应用返回）. 如: ca51d83b
	ChatOwnerName       string                              `json:"chat_owner_name,omitempty"`        // 群主姓名. 如: xxx
	ChatOwnerOpenID     string                              `json:"chat_owner_open_id,omitempty"`     // 群主的open_id. 如: ou_18eac85d35a26f989317ad4f02e8bbbb
	OpenChatID          string                              `json:"open_chat_id,omitempty"`           // 群聊的id. 如: oc_e520983d3e4f5ec7306069bffe672aa3
	OperatorEmployeeID  string                              `json:"operator_employee_id,omitempty"`   // 操作者的emplolyee_id ，仅企业自建应用返回. 如: ca51d83b
	OperatorName        string                              `json:"operator_name,omitempty"`          // 操作者姓名. 如: yyy
	OperatorOpenID      string                              `json:"operator_open_id,omitempty"`       // 操作者的open_id. 如: ou_18eac85d35a26f989317ad4f02e8bbbb
	OwnerIsBot          bool                                `json:"owner_is_bot,omitempty"`           // 群主是否是机器人. 如: false
	TenantKey           string                              `json:"tenant_key,omitempty"`             // 企业标识. 如: 736588c9260f175d
	Type                string                              `json:"type,omitempty"`                   // 移除机器人：remove_bot. 如: remove_bot
}

// EventV1RemoveBotEventChatI18nNames ...
type EventV1RemoveBotEventChatI18nNames struct {
	EnUs string `json:"en_us,omitempty"` // 如: 英文标题
	ZhCn string `json:"zh_cn,omitempty"` // 如: 中文标题
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1RemoveUserFromChat
//
// 如果你希望订阅机器人进出群、群内有人@机器人事件，请前往[机器人进群](https://open.feishu.cn/document/ukTMukTMukTM/ugzMugzMugzM/event/bot-added-to-group) 或 [机器人退群](https://open.feishu.cn/document/ukTMukTMukTM/ucDO04yN4QjL3gDN)。
// 为了更好地提升该事件的安全性，我们对其进行了升级，请尽快迁移至
// [新版本（用户进群）](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-user/events/added)
// 或[新版本（用户出群）>>](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-user/events/deleted)
// 用户进群、出群后触发此事件。
// * 特殊说明：只有开启机器人能力并且机器人所在的群发生上述变化时才能触发此事件。
// 事件包括三个类型：
// 1. 用户进群 - add_user_to_chat
// 2. 用户出群 - remove_user_from_chat
// 3. 撤销加人 - revoke_add_user_from_chat
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uQDOwUjL0gDM14CN4ATN/event/user-joins-or-leave-group
func (r *EventCallbackService) HandlerEventV1RemoveUserFromChat(f EventV1RemoveUserFromChatHandler) {
	r.cli.eventHandler.eventV1RemoveUserFromChatHandler = f
}

// EventV1RemoveUserFromChatHandler event EventV1RemoveUserFromChat handler
type EventV1RemoveUserFromChatHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1RemoveUserFromChat) (string, error)

// EventV1RemoveUserFromChat ...
type EventV1RemoveUserFromChat struct {
	AppID     string                                  `json:"app_id,omitempty"`     // 如: cli_9c8609450f78d102
	ChatID    string                                  `json:"chat_id,omitempty"`    // 群聊的id. 如: oc_9e9619b938c9571c1c3165681cdaead5
	Operator  *EventV1RemoveUserFromChatEventOperator `json:"operator,omitempty"`   // 用户进出群的操作人。用户主动退群的话，operator 就是user自己
	TenantKey string                                  `json:"tenant_key,omitempty"` // 如: 736588c9260f175d
	Type      string                                  `json:"type,omitempty"`       // 事件类型，add_user_to_chat/remove_user_from_chat/revoke_add_user_from_chat. 如: add_user_to_chat
	Users     []*EventV1RemoveUserFromChatEventUser   `json:"users,omitempty"`
}

// EventV1RemoveUserFromChatEventOperator ...
type EventV1RemoveUserFromChatEventOperator struct {
	OpenID string `json:"open_id,omitempty"` // 员工对此应用的唯一标识，同一员工对不同应用的open_id不同. 如: ou_18eac85d35a26f989317ad4f02e8bbbb
	UserID string `json:"user_id,omitempty"` // 即“用户ID”，仅企业自建应用会返回. 如: ca51d83b
}

// EventV1RemoveUserFromChatEventUser ...
type EventV1RemoveUserFromChatEventUser struct {
	Name   string `json:"name,omitempty"`    // 如: James
	OpenID string `json:"open_id,omitempty"` // 如: ou_706adeb944ab1473b9fb3e7da2a40b68
	UserID string `json:"user_id,omitempty"` // 如: 51g97a4g
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1RevokeAddUserFromChat
//
// 如果你希望订阅机器人进出群、群内有人@机器人事件，请前往[机器人进群](https://open.feishu.cn/document/ukTMukTMukTM/ugzMugzMugzM/event/bot-added-to-group) 或 [机器人退群](https://open.feishu.cn/document/ukTMukTMukTM/ucDO04yN4QjL3gDN)。
// 为了更好地提升该事件的安全性，我们对其进行了升级，请尽快迁移至
// [新版本（用户进群）](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-user/events/added)
// 或[新版本（用户出群）>>](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-user/events/deleted)
// 用户进群、出群后触发此事件。
// * 特殊说明：只有开启机器人能力并且机器人所在的群发生上述变化时才能触发此事件。
// 事件包括三个类型：
// 1. 用户进群 - add_user_to_chat
// 2. 用户出群 - remove_user_from_chat
// 3. 撤销加人 - revoke_add_user_from_chat
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uQDOwUjL0gDM14CN4ATN/event/user-joins-or-leave-group
func (r *EventCallbackService) HandlerEventV1RevokeAddUserFromChat(f EventV1RevokeAddUserFromChatHandler) {
	r.cli.eventHandler.eventV1RevokeAddUserFromChatHandler = f
}

// EventV1RevokeAddUserFromChatHandler event EventV1RevokeAddUserFromChat handler
type EventV1RevokeAddUserFromChatHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1RevokeAddUserFromChat) (string, error)

// EventV1RevokeAddUserFromChat ...
type EventV1RevokeAddUserFromChat struct {
	AppID     string                                     `json:"app_id,omitempty"`     // 如: cli_9c8609450f78d102
	ChatID    string                                     `json:"chat_id,omitempty"`    // 群聊的id. 如: oc_9e9619b938c9571c1c3165681cdaead5
	Operator  *EventV1RevokeAddUserFromChatEventOperator `json:"operator,omitempty"`   // 用户进出群的操作人。用户主动退群的话，operator 就是user自己
	TenantKey string                                     `json:"tenant_key,omitempty"` // 如: 736588c9260f175d
	Type      string                                     `json:"type,omitempty"`       // 事件类型，add_user_to_chat/remove_user_from_chat/revoke_add_user_from_chat. 如: add_user_to_chat
	Users     []*EventV1RevokeAddUserFromChatEventUser   `json:"users,omitempty"`
}

// EventV1RevokeAddUserFromChatEventOperator ...
type EventV1RevokeAddUserFromChatEventOperator struct {
	OpenID string `json:"open_id,omitempty"` // 员工对此应用的唯一标识，同一员工对不同应用的open_id不同. 如: ou_18eac85d35a26f989317ad4f02e8bbbb
	UserID string `json:"user_id,omitempty"` // 即“用户ID”，仅企业自建应用会返回. 如: ca51d83b
}

// EventV1RevokeAddUserFromChatEventUser ...
type EventV1RevokeAddUserFromChatEventUser struct {
	Name   string `json:"name,omitempty"`    // 如: James
	OpenID string `json:"open_id,omitempty"` // 如: ou_706adeb944ab1473b9fb3e7da2a40b68
	UserID string `json:"user_id,omitempty"` // 如: 51g97a4g
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1ShiftApproval
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 通过open api创建的换班申请审批通过后触发此事件。备注：「打卡」应用里提交换班申请不支持该事件。即将下线，已订阅的不受影响。
// * 依赖权限：[访问审批应用]
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uIDO24iM4YjLygjN/event/shift-change
func (r *EventCallbackService) HandlerEventV1ShiftApproval(f EventV1ShiftApprovalHandler) {
	r.cli.eventHandler.eventV1ShiftApprovalHandler = f
}

// EventV1ShiftApprovalHandler event EventV1ShiftApproval handler
type EventV1ShiftApprovalHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1ShiftApproval) (string, error)

// EventV1ShiftApproval ...
type EventV1ShiftApproval struct {
	AppID        string `json:"app_id,omitempty"`        // 如: cli_xxx
	TenantKey    string `json:"tenant_key,omitempty"`    // 如: xxx
	Type         string `json:"type,omitempty"`          // 如: shift_approval
	InstanceCode string `json:"instance_code,omitempty"` // 审批实例Code. 如: xxx
	EmployeeID   string `json:"employee_id,omitempty"`   // 用户id. 如: xxx
	OpenID       string `json:"open_id,omitempty"`       // 用户open_id. 如: ou_xxx
	StartTime    int64  `json:"start_time,omitempty"`    // 审批发起时间. 如: 1502199207
	EndTime      int64  `json:"end_time,omitempty"`      // 审批结束时间. 如: 1502199307
	ShiftTime    string `json:"shift_time,omitempty"`    // 换班时间. 如: 2018-12-01 12:00:00
	ReturnTime   string `json:"return_time,omitempty"`   // 还班时间. 如: 2018-12-02 12:00:00
	ShiftReason  string `json:"shift_reason,omitempty"`  // 换班事由. 如: xxx
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1ThirdPartyMeetingRoomEventCreated
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 当添加了第三方会议室的日程发生变动时（创建/更新/删除）触发此事件。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/meeting_room-v1/event/third-room-event-changes
func (r *EventCallbackService) HandlerEventV1ThirdPartyMeetingRoomEventCreated(f EventV1ThirdPartyMeetingRoomEventCreatedHandler) {
	r.cli.eventHandler.eventV1ThirdPartyMeetingRoomEventCreatedHandler = f
}

// EventV1ThirdPartyMeetingRoomEventCreatedHandler event EventV1ThirdPartyMeetingRoomEventCreated handler
type EventV1ThirdPartyMeetingRoomEventCreatedHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1ThirdPartyMeetingRoomEventCreated) (string, error)

// EventV1ThirdPartyMeetingRoomEventCreated ...
type EventV1ThirdPartyMeetingRoomEventCreated struct {
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1ThirdPartyMeetingRoomEventDeleted
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 当添加了第三方会议室的日程发生变动时（创建/更新/删除）触发此事件。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/meeting_room-v1/event/third-room-event-changes
func (r *EventCallbackService) HandlerEventV1ThirdPartyMeetingRoomEventDeleted(f EventV1ThirdPartyMeetingRoomEventDeletedHandler) {
	r.cli.eventHandler.eventV1ThirdPartyMeetingRoomEventDeletedHandler = f
}

// EventV1ThirdPartyMeetingRoomEventDeletedHandler event EventV1ThirdPartyMeetingRoomEventDeleted handler
type EventV1ThirdPartyMeetingRoomEventDeletedHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1ThirdPartyMeetingRoomEventDeleted) (string, error)

// EventV1ThirdPartyMeetingRoomEventDeleted ...
type EventV1ThirdPartyMeetingRoomEventDeleted struct {
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1ThirdPartyMeetingRoomEventUpdated
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 当添加了第三方会议室的日程发生变动时（创建/更新/删除）触发此事件。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/meeting_room-v1/event/third-room-event-changes
func (r *EventCallbackService) HandlerEventV1ThirdPartyMeetingRoomEventUpdated(f EventV1ThirdPartyMeetingRoomEventUpdatedHandler) {
	r.cli.eventHandler.eventV1ThirdPartyMeetingRoomEventUpdatedHandler = f
}

// EventV1ThirdPartyMeetingRoomEventUpdatedHandler event EventV1ThirdPartyMeetingRoomEventUpdated handler
type EventV1ThirdPartyMeetingRoomEventUpdatedHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1ThirdPartyMeetingRoomEventUpdated) (string, error)

// EventV1ThirdPartyMeetingRoomEventUpdated ...
type EventV1ThirdPartyMeetingRoomEventUpdated struct {
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1TripApproval
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 「审批」应用的表单里如果包含 [出差控件组]，则在此表单审批通过后触发此事件。
// * 依赖权限：[访问审批应用]
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uIDO24iM4YjLygjN/event/business-trip
func (r *EventCallbackService) HandlerEventV1TripApproval(f EventV1TripApprovalHandler) {
	r.cli.eventHandler.eventV1TripApprovalHandler = f
}

// EventV1TripApprovalHandler event EventV1TripApproval handler
type EventV1TripApprovalHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1TripApproval) (string, error)

// EventV1TripApproval ...
type EventV1TripApproval struct {
	AppID        string                              `json:"app_id,omitempty"`        // 如: cli_xxx
	TenantKey    string                              `json:"tenant_key,omitempty"`    // 如: xxx
	Type         string                              `json:"type,omitempty"`          // 如: trip_approval
	InstanceCode string                              `json:"instance_code,omitempty"` // 审批实例Code. 如: xxx
	EmployeeID   string                              `json:"employee_id,omitempty"`   // 用户id. 如: xxx
	OpenID       string                              `json:"open_id,omitempty"`       // 用户open_id. 如: ou_xxx
	StartTime    int64                               `json:"start_time,omitempty"`    // 审批发起时间. 如: 1502199207
	EndTime      int64                               `json:"end_time,omitempty"`      // 审批结束时间. 如: 1502199307
	Schedules    []*EventV1TripApprovalEventSchedule `json:"schedules,omitempty"`
	TripInterval int64                               `json:"trip_interval,omitempty"` // 行程总时长（秒）. 如: 3600
	TripReason   string                              `json:"trip_reason,omitempty"`   // 出差事由. 如: xxx
	TripPeers    []*EventV1TripApprovalEventTripPeer `json:"trip_peers,omitempty"`    // 同行人
}

// EventV1TripApprovalEventSchedule ...
type EventV1TripApprovalEventSchedule struct {
	TripStartTime  string `json:"trip_start_time,omitempty"` // 行程开始时间. 如: 2018-12-01 12:00:00
	TripEndTime    string `json:"trip_end_time,omitempty"`   // 行程结束时间. 如: 2018-12-01 12:00:00
	TripInterval   int64  `json:"trip_interval,omitempty"`   // 行程时长（秒）. 如: 3600
	Departure      string `json:"departure,omitempty"`       // 出发地. 如: xxx
	Destination    string `json:"destination,omitempty"`     // 目的地. 如: xxx
	Transportation string `json:"transportation,omitempty"`  // 交通工具. 如: xxx
	TripType       string `json:"trip_type,omitempty"`       // 单程/往返. 如: 单程
	Remark         string `json:"remark,omitempty"`          // 备注. 如: 备注
}

// EventV1TripApprovalEventTripPeer ...
type EventV1TripApprovalEventTripPeer struct {
	string `json:",omitempty"` // 如: xxx
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV1WorkApproval
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 「审批」应用的表单里如果包含 [加班控件组]，则在此表单审批通过后触发此事件。
// * 依赖权限：[访问审批应用]
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uIDO24iM4YjLygjN/event/overtime
func (r *EventCallbackService) HandlerEventV1WorkApproval(f EventV1WorkApprovalHandler) {
	r.cli.eventHandler.eventV1WorkApprovalHandler = f
}

// EventV1WorkApprovalHandler event EventV1WorkApproval handler
type EventV1WorkApprovalHandler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV1, event *EventV1WorkApproval) (string, error)

// EventV1WorkApproval ...
type EventV1WorkApproval struct {
	AppID         string `json:"app_id,omitempty"`          // 如: cli_xxx
	TenantKey     string `json:"tenant_key,omitempty"`      // 如: xxx
	Type          string `json:"type,omitempty"`            // 如: work_approval
	InstanceCode  string `json:"instance_code,omitempty"`   // 审批实例Code. 如: xxx
	EmployeeID    string `json:"employee_id,omitempty"`     // 用户id. 如: xxx
	OpenID        string `json:"open_id,omitempty"`         // 用户open_id. 如: ou_xxx
	StartTime     int64  `json:"start_time,omitempty"`      // 审批发起时间. 如: 1502199207
	EndTime       int64  `json:"end_time,omitempty"`        // 审批结束时间. 如: 1502199307
	WorkType      string `json:"work_type,omitempty"`       // 加班类型. 如: xxx
	WorkStartTime string `json:"work_start_time,omitempty"` // 加班开始时间. 如: 2018-12-01 12:00:00
	WorkEndTime   string `json:"work_end_time,omitempty"`   // 加班结束时间. 如: 2018-12-02 12:00:00
	WorkInterval  int64  `json:"work_interval,omitempty"`   // 加班时长，单位（秒）. 如: 7200
	WorkReason    string `json:"work_reason,omitempty"`     // 加班事由. 如: xxx
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ACSAccessRecordCreatedV1
//
// 门禁设备识别用户成功后发送该事件给订阅应用{使用示例}(url=/api/tools/api_explore/api_explore_config?project=acs&version=v1&resource=access_record&event=created)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/acs-v1/access_record/events/created
func (r *EventCallbackService) HandlerEventV2ACSAccessRecordCreatedV1(f EventV2ACSAccessRecordCreatedV1Handler) {
	r.cli.eventHandler.eventV2ACSAccessRecordCreatedV1Handler = f
}

// EventV2ACSAccessRecordCreatedV1Handler event EventV2ACSAccessRecordCreatedV1 handler
type EventV2ACSAccessRecordCreatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ACSAccessRecordCreatedV1) (string, error)

// EventV2ACSAccessRecordCreatedV1 ...
type EventV2ACSAccessRecordCreatedV1 struct {
	AccessRecordID string                                 `json:"access_record_id,omitempty"` // 门禁记录 ID
	UserID         *EventV2ACSAccessRecordCreatedV1UserID `json:"user_id,omitempty"`          // 用户 ID
	DeviceID       string                                 `json:"device_id,omitempty"`        // 设备 ID
	IsClockIn      bool                                   `json:"is_clock_in,omitempty"`      // 是否打卡
	IsDoorOpen     bool                                   `json:"is_door_open,omitempty"`     // 是否开门
	AccessTime     string                                 `json:"access_time,omitempty"`      // 识别时间 （单位：秒）
}

// EventV2ACSAccessRecordCreatedV1UserID ...
type EventV2ACSAccessRecordCreatedV1UserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ACSUserUpdatedV1
//
// 智能门禁用户特征值变化时，发送此事件{使用示例}(url=/api/tools/api_explore/api_explore_config?project=acs&version=v1&resource=user&event=updated)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/acs-v1/user/events/updated
func (r *EventCallbackService) HandlerEventV2ACSUserUpdatedV1(f EventV2ACSUserUpdatedV1Handler) {
	r.cli.eventHandler.eventV2ACSUserUpdatedV1Handler = f
}

// EventV2ACSUserUpdatedV1Handler event EventV2ACSUserUpdatedV1 handler
type EventV2ACSUserUpdatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ACSUserUpdatedV1) (string, error)

// EventV2ACSUserUpdatedV1 ...
type EventV2ACSUserUpdatedV1 struct {
	UserID       *EventV2ACSUserUpdatedV1UserID `json:"user_id,omitempty"`       // 用户 ID
	Card         int64                          `json:"card,omitempty"`          // 卡号
	FaceUploaded bool                           `json:"face_uploaded,omitempty"` // 是否上传人脸图片
}

// EventV2ACSUserUpdatedV1UserID ...
type EventV2ACSUserUpdatedV1UserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ApplicationApplicationAppVersionAuditV6
//
// 通过订阅该事件，可接收应用审核（通过 / 拒绝）事件{使用示例}(url=/api/tools/api_explore/api_explore_config?project=application&version=v6&resource=application.app_version&event=audit)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/application-app_version/events/audit
func (r *EventCallbackService) HandlerEventV2ApplicationApplicationAppVersionAuditV6(f EventV2ApplicationApplicationAppVersionAuditV6Handler) {
	r.cli.eventHandler.eventV2ApplicationApplicationAppVersionAuditV6Handler = f
}

// EventV2ApplicationApplicationAppVersionAuditV6Handler event EventV2ApplicationApplicationAppVersionAuditV6 handler
type EventV2ApplicationApplicationAppVersionAuditV6Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ApplicationApplicationAppVersionAuditV6) (string, error)

// EventV2ApplicationApplicationAppVersionAuditV6 ...
type EventV2ApplicationApplicationAppVersionAuditV6 struct {
	OperatorID  *EventV2ApplicationApplicationAppVersionAuditV6OperatorID `json:"operator_id,omitempty"`  // 通过 / 拒绝应用审核的管理员 id
	VersionID   string                                                    `json:"version_id,omitempty"`   // 被审核的应用版本 id
	CreatorID   *EventV2ApplicationApplicationAppVersionAuditV6CreatorID  `json:"creator_id,omitempty"`   // 应用创建者的 id
	AppID       string                                                    `json:"app_id,omitempty"`       // 撤回应用的 id
	Operation   string                                                    `json:"operation,omitempty"`    // 审核通过 / 拒绝, 可选值有: `audited`：审核通过, `reject`：审核拒绝
	Remark      string                                                    `json:"remark,omitempty"`       // 审核信息，当审核拒绝时，管理员填写的拒绝理由
	AuditSource string                                                    `json:"audit_source,omitempty"` // 应用审核的方式, 可选值有: `administrator`：管理员审核, `auto`：自动免审
}

// EventV2ApplicationApplicationAppVersionAuditV6OperatorID ...
type EventV2ApplicationApplicationAppVersionAuditV6OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求:  获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2ApplicationApplicationAppVersionAuditV6CreatorID ...
type EventV2ApplicationApplicationAppVersionAuditV6CreatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求:  获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ApplicationApplicationAppVersionPublishApplyV6
//
// 通过订阅该事件，可接收应用提交发布申请事件{使用示例}(url=/api/tools/api_explore/api_explore_config?project=application&version=v6&resource=application.app_version&event=publish_apply)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/application-app_version/events/publish_apply
func (r *EventCallbackService) HandlerEventV2ApplicationApplicationAppVersionPublishApplyV6(f EventV2ApplicationApplicationAppVersionPublishApplyV6Handler) {
	r.cli.eventHandler.eventV2ApplicationApplicationAppVersionPublishApplyV6Handler = f
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6Handler event EventV2ApplicationApplicationAppVersionPublishApplyV6 handler
type EventV2ApplicationApplicationAppVersionPublishApplyV6Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ApplicationApplicationAppVersionPublishApplyV6) (string, error)

// EventV2ApplicationApplicationAppVersionPublishApplyV6 ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6 struct {
	OperatorID        *EventV2ApplicationApplicationAppVersionPublishApplyV6OperatorID        `json:"operator_id,omitempty"`         // 用户 ID
	OnlineVersion     *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersion     `json:"online_version,omitempty"`      // 当前线上版本信息
	UnderAuditVersion *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersion `json:"under_audit_version,omitempty"` // 当前线上版本信息
	AppStatus         int64                                                                   `json:"app_status,omitempty"`          // 应用状态, 可选值有: `0`：停用状态, `1`：启用状态
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OperatorID ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersion ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersion struct {
	AppID            string                                                                     `json:"app_id,omitempty"`            // 应用 id
	Version          string                                                                     `json:"version,omitempty"`           // 开发者填入的应用版本 ID, 最小长度：`1` 字符
	VersionID        string                                                                     `json:"version_id,omitempty"`        // 唯一标识应用版本的 ID
	AppName          string                                                                     `json:"app_name,omitempty"`          // 应用默认名称, 最小长度：`1` 字符
	AvatarURL        string                                                                     `json:"avatar_url,omitempty"`        // 应用头像 url
	Description      string                                                                     `json:"description,omitempty"`       // 应用默认描述, 最小长度：`1` 字符
	Scopes           []*EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionScope `json:"scopes,omitempty"`            // 应用权限列表
	BackHomeURL      string                                                                     `json:"back_home_url,omitempty"`     // 后台主页地址
	I18n             []*EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionI18n  `json:"i18n,omitempty"`              // 应用的国际化信息列表, 最小长度：`1`
	CommonCategories []string                                                                   `json:"common_categories,omitempty"` // 应用分类的国际化描述, 最大长度：`3`
	Events           []string                                                                   `json:"events,omitempty"`            // 应用已订阅开放平台事件列表
	Status           int64                                                                      `json:"status,omitempty"`            // 版本状态, 可选值有: `0`：未知状态, `1`：审核通过, `2`：审核拒绝, `3`：审核中, `4`：未提交审核
	CreateTime       string                                                                     `json:"create_time,omitempty"`       // 版本创建时间（单位：s）
	PublishTime      string                                                                     `json:"publish_time,omitempty"`      // 版本发布时间（单位：s）
	Ability          *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbility `json:"ability,omitempty"`           // 当前版本下应用开启的能力
	Remark           *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemark  `json:"remark,omitempty"`            // 跟随应用版本的信息
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionScope ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionScope struct {
	Scope       string `json:"scope,omitempty"`       // 应用权限
	Description string `json:"description,omitempty"` // 应用权限的国际化描述
	Level       int64  `json:"level,omitempty"`       // 权限等级描述, 可选值有: `1`：普通权限, `2`：高级权限, `3`：超敏感权限, `0`：未知等级
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionI18n ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionI18n struct {
	I18nKey     string `json:"i18n_key,omitempty"`    // 国际化语言的 key, 可选值有: `zh_cn`：中文, `en_us`：英文, `ja_jp`：日文
	Name        string `json:"name,omitempty"`        // 应用国际化名称, 最小长度：`1` 字符
	Description string `json:"description,omitempty"` // 应用国际化描述（副标题）, 最小长度：`1` 字符
	HelpUse     string `json:"help_use,omitempty"`    // 帮助国际化文档链接
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbility ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbility struct {
	Gadget           *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityGadget            `json:"gadget,omitempty"`            // 小程序能力
	WebApp           *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityWebApp            `json:"web_app,omitempty"`           // 网页能力
	Bot              *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityBot               `json:"bot,omitempty"`               // 机器人能力
	WorkplaceWidgets []*EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityWorkplaceWidget `json:"workplace_widgets,omitempty"` // 小组件能力
	Navigate         *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityNavigate          `json:"navigate,omitempty"`          // 主导航小程序
	CloudDoc         *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityCloudDoc          `json:"cloud_doc,omitempty"`         // 云文档应用
	DocsBlocks       []*EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityDocsBlock       `json:"docs_blocks,omitempty"`       // 云文档小组件
	MessageAction    *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityMessageAction     `json:"message_action,omitempty"`    // 消息快捷操作
	PlusMenu         *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityPlusMenu          `json:"plus_menu,omitempty"`         // 加号菜单
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityGadget ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityGadget struct {
	EnablePcMode         int64    `json:"enable_pc_mode,omitempty"`          // pc 支持的小程序模式，bit 位表示, 可选值有: `1`：sidebar 模式, `2`：pc 模式, `4`：主导航模式
	SchemaURLs           []string `json:"schema_urls,omitempty"`             // schema url 列表
	PcUseMobilePkg       bool     `json:"pc_use_mobile_pkg,omitempty"`       // pc 端是否使用小程序版本
	PcVersion            string   `json:"pc_version,omitempty"`              // pc 的小程序版本号, 最小长度：`1` 字符
	MobileVersion        string   `json:"mobile_version,omitempty"`          // 移动端小程序版本号, 最小长度：`1` 字符
	MobileMinLarkVersion string   `json:"mobile_min_lark_version,omitempty"` // 移动端兼容的最低飞书版本, 最小长度：`1` 字符
	PcMinLarkVersion     string   `json:"pc_min_lark_version,omitempty"`     // pc 端兼容的最低飞书版本, 最小长度：`1` 字符
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityWebApp ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityWebApp struct {
	PcURL     string `json:"pc_url,omitempty"`     // pc 端 url
	MobileURL string `json:"mobile_url,omitempty"` // 移动端 url
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityBot ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityBot struct {
	CardRequestURL string `json:"card_request_url,omitempty"` // 消息卡片回调地址
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityWorkplaceWidget ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityWorkplaceWidget struct {
	MinLarkVersion string `json:"min_lark_version,omitempty"` // 最低兼容 lark 版本号
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityNavigate ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityNavigate struct {
	Pc     *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityNavigatePc     `json:"pc,omitempty"`     // pc 端主导航信息
	Mobile *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityNavigateMobile `json:"mobile,omitempty"` // 移动端主导航信息
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityNavigatePc ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityNavigatePc struct {
	Version       string `json:"version,omitempty"`         // 主导航小程序版本号, 最小长度：`1` 字符
	ImageURL      string `json:"image_url,omitempty"`       // 默认图片 url
	HoverImageURL string `json:"hover_image_url,omitempty"` // 选中态图片 url
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityNavigateMobile ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityNavigateMobile struct {
	Version       string `json:"version,omitempty"`         // 主导航小程序版本号, 最小长度：`1` 字符
	ImageURL      string `json:"image_url,omitempty"`       // 默认图片 url
	HoverImageURL string `json:"hover_image_url,omitempty"` // 选中态图片 url
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityCloudDoc ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityCloudDoc struct {
	SpaceURL string                                                                                   `json:"space_url,omitempty"` // 云空间重定向 url
	I18n     []*EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityCloudDocI18n `json:"i18n,omitempty"`      // 国际化信息, 最小长度：`1`
	IconURL  string                                                                                   `json:"icon_url,omitempty"`  // 图标链接
	Mode     int64                                                                                    `json:"mode,omitempty"`      // 云文档支持模式, 可选值有: `0`：未知, `1`：移动端
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityCloudDocI18n ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityCloudDocI18n struct {
	I18nKey          string `json:"i18n_key,omitempty"`          // 国际化语言的 key, 可选值有: `zh_cn`：中文, `en_us`：英文, `ja_jp`：日文
	Name             string `json:"name,omitempty"`              // 云文档国际化名称
	ReadDescription  string `json:"read_description,omitempty"`  // 云文档国际化读权限说明
	WriteDescription string `json:"write_description,omitempty"` // 云文档国际化写权限说明
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityDocsBlock ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityDocsBlock struct {
	BlockTypeID   string                                                                                    `json:"block_type_id,omitempty"`   // BlockTypeID
	I18n          []*EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityDocsBlockI18n `json:"i18n,omitempty"`            // block 的国际化信息, 最小长度：`1`
	MobileIconURL string                                                                                    `json:"mobile_icon_url,omitempty"` // 移动端 icon 链接
	PcIconURL     string                                                                                    `json:"pc_icon_url,omitempty"`     // pc 端口 icon 链接
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityDocsBlockI18n ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityDocsBlockI18n struct {
	I18nKey string `json:"i18n_key,omitempty"` // 国际化语言的 key, 可选值有: `zh_cn`：中文, `en_us`：英文, `ja_jp`：日文
	Name    string `json:"name,omitempty"`     // 名称, 最小长度：`1` 字符
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityMessageAction ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityMessageAction struct {
	PcAppLink     string                                                                                        `json:"pc_app_link,omitempty"`     // pc 端链接
	MobileAppLink string                                                                                        `json:"mobile_app_link,omitempty"` // 移动端链接
	I18n          []*EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityMessageActionI18n `json:"i18n,omitempty"`            // 国际化信息, 最小长度：`1`
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityMessageActionI18n ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityMessageActionI18n struct {
	I18nKey string `json:"i18n_key,omitempty"` // 国际化语言的 key, 可选值有: `zh_cn`：中文, `en_us`：英文, `ja_jp`：日文
	Name    string `json:"name,omitempty"`     // 国际化名称, 最小长度：`1` 字符
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityPlusMenu ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionAbilityPlusMenu struct {
	PcAppLink     string `json:"pc_app_link,omitempty"`     // pc 端链接
	MobileAppLink string `json:"mobile_app_link,omitempty"` // 移动端链接
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemark ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemark struct {
	Remark       string                                                                              `json:"remark,omitempty"`        // 备注说明
	UpdateRemark string                                                                              `json:"update_remark,omitempty"` // 更新说明
	Visibility   *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibility `json:"visibility,omitempty"`    // 可见性名单
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibility ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibility struct {
	IsAll         bool                                                                                             `json:"is_all,omitempty"`         // 是否全员可见
	VisibleList   *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityVisibleList   `json:"visible_list,omitempty"`   // 可见名单
	InvisibleList *EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityInvisibleList `json:"invisible_list,omitempty"` // 不可见名单
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityVisibleList ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityVisibleList struct {
	OpenIDs       []*EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityVisibleListOpenID `json:"open_ids,omitempty"`       // 可见性成员 id 列表
	DepartmentIDs []string                                                                                               `json:"department_ids,omitempty"` // 可见性部门的 id 列表
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityVisibleListOpenID ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityVisibleListOpenID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityInvisibleList ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityInvisibleList struct {
	OpenIDs       []*EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityInvisibleListOpenID `json:"open_ids,omitempty"`       // 可见性成员 id 列表
	DepartmentIDs []string                                                                                                 `json:"department_ids,omitempty"` // 可见性部门的 id 列表
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityInvisibleListOpenID ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6OnlineVersionRemarkVisibilityInvisibleListOpenID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersion ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersion struct {
	AppID            string                                                                         `json:"app_id,omitempty"`            // 应用 id
	Version          string                                                                         `json:"version,omitempty"`           // 开发者填入的应用版本 ID, 最小长度：`1` 字符
	VersionID        string                                                                         `json:"version_id,omitempty"`        // 唯一标识应用版本的 ID
	AppName          string                                                                         `json:"app_name,omitempty"`          // 应用默认名称, 最小长度：`1` 字符
	AvatarURL        string                                                                         `json:"avatar_url,omitempty"`        // 应用头像 url
	Description      string                                                                         `json:"description,omitempty"`       // 应用默认描述, 最小长度：`1` 字符
	Scopes           []*EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionScope `json:"scopes,omitempty"`            // 应用权限列表
	BackHomeURL      string                                                                         `json:"back_home_url,omitempty"`     // 后台主页地址
	I18n             []*EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionI18n  `json:"i18n,omitempty"`              // 应用的国际化信息列表, 最小长度：`1`
	CommonCategories []string                                                                       `json:"common_categories,omitempty"` // 应用分类的国际化描述, 最大长度：`3`
	Events           []string                                                                       `json:"events,omitempty"`            // 应用已订阅开放平台事件列表
	Status           int64                                                                          `json:"status,omitempty"`            // 版本状态, 可选值有: `0`：未知状态, `1`：审核通过, `2`：审核拒绝, `3`：审核中, `4`：未提交审核
	CreateTime       string                                                                         `json:"create_time,omitempty"`       // 版本创建时间（单位：s）
	PublishTime      string                                                                         `json:"publish_time,omitempty"`      // 版本发布时间（单位：s）
	Ability          *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbility `json:"ability,omitempty"`           // 当前版本下应用开启的能力
	Remark           *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemark  `json:"remark,omitempty"`            // 跟随应用版本的信息
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionScope ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionScope struct {
	Scope       string `json:"scope,omitempty"`       // 应用权限
	Description string `json:"description,omitempty"` // 应用权限的国际化描述
	Level       int64  `json:"level,omitempty"`       // 权限等级描述, 可选值有: `1`：普通权限, `2`：高级权限, `3`：超敏感权限, `0`：未知等级
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionI18n ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionI18n struct {
	I18nKey     string `json:"i18n_key,omitempty"`    // 国际化语言的 key, 可选值有: `zh_cn`：中文, `en_us`：英文, `ja_jp`：日文
	Name        string `json:"name,omitempty"`        // 应用国际化名称, 最小长度：`1` 字符
	Description string `json:"description,omitempty"` // 应用国际化描述（副标题）, 最小长度：`1` 字符
	HelpUse     string `json:"help_use,omitempty"`    // 帮助国际化文档链接
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbility ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbility struct {
	Gadget           *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityGadget            `json:"gadget,omitempty"`            // 小程序能力
	WebApp           *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityWebApp            `json:"web_app,omitempty"`           // 网页能力
	Bot              *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityBot               `json:"bot,omitempty"`               // 机器人能力
	WorkplaceWidgets []*EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityWorkplaceWidget `json:"workplace_widgets,omitempty"` // 小组件能力
	Navigate         *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityNavigate          `json:"navigate,omitempty"`          // 主导航小程序
	CloudDoc         *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityCloudDoc          `json:"cloud_doc,omitempty"`         // 云文档应用
	DocsBlocks       []*EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityDocsBlock       `json:"docs_blocks,omitempty"`       // 云文档小组件
	MessageAction    *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityMessageAction     `json:"message_action,omitempty"`    // 消息快捷操作
	PlusMenu         *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityPlusMenu          `json:"plus_menu,omitempty"`         // 加号菜单
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityGadget ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityGadget struct {
	EnablePcMode         int64    `json:"enable_pc_mode,omitempty"`          // pc 支持的小程序模式，bit 位表示, 可选值有: `1`：sidebar 模式, `2`：pc 模式, `4`：主导航模式
	SchemaURLs           []string `json:"schema_urls,omitempty"`             // schema url 列表
	PcUseMobilePkg       bool     `json:"pc_use_mobile_pkg,omitempty"`       // pc 端是否使用小程序版本
	PcVersion            string   `json:"pc_version,omitempty"`              // pc 的小程序版本号, 最小长度：`1` 字符
	MobileVersion        string   `json:"mobile_version,omitempty"`          // 移动端小程序版本号, 最小长度：`1` 字符
	MobileMinLarkVersion string   `json:"mobile_min_lark_version,omitempty"` // 移动端兼容的最低飞书版本, 最小长度：`1` 字符
	PcMinLarkVersion     string   `json:"pc_min_lark_version,omitempty"`     // pc 端兼容的最低飞书版本, 最小长度：`1` 字符
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityWebApp ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityWebApp struct {
	PcURL     string `json:"pc_url,omitempty"`     // pc 端 url
	MobileURL string `json:"mobile_url,omitempty"` // 移动端 url
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityBot ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityBot struct {
	CardRequestURL string `json:"card_request_url,omitempty"` // 消息卡片回调地址
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityWorkplaceWidget ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityWorkplaceWidget struct {
	MinLarkVersion string `json:"min_lark_version,omitempty"` // 最低兼容 lark 版本号
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityNavigate ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityNavigate struct {
	Pc     *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityNavigatePc     `json:"pc,omitempty"`     // pc 端主导航信息
	Mobile *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityNavigateMobile `json:"mobile,omitempty"` // 移动端主导航信息
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityNavigatePc ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityNavigatePc struct {
	Version       string `json:"version,omitempty"`         // 主导航小程序版本号, 最小长度：`1` 字符
	ImageURL      string `json:"image_url,omitempty"`       // 默认图片 url
	HoverImageURL string `json:"hover_image_url,omitempty"` // 选中态图片 url
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityNavigateMobile ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityNavigateMobile struct {
	Version       string `json:"version,omitempty"`         // 主导航小程序版本号, 最小长度：`1` 字符
	ImageURL      string `json:"image_url,omitempty"`       // 默认图片 url
	HoverImageURL string `json:"hover_image_url,omitempty"` // 选中态图片 url
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityCloudDoc ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityCloudDoc struct {
	SpaceURL string                                                                                       `json:"space_url,omitempty"` // 云空间重定向 url
	I18n     []*EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityCloudDocI18n `json:"i18n,omitempty"`      // 国际化信息, 最小长度：`1`
	IconURL  string                                                                                       `json:"icon_url,omitempty"`  // 图标链接
	Mode     int64                                                                                        `json:"mode,omitempty"`      // 云文档支持模式, 可选值有: `0`：未知, `1`：移动端
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityCloudDocI18n ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityCloudDocI18n struct {
	I18nKey          string `json:"i18n_key,omitempty"`          // 国际化语言的 key, 可选值有: `zh_cn`：中文, `en_us`：英文, `ja_jp`：日文
	Name             string `json:"name,omitempty"`              // 云文档国际化名称
	ReadDescription  string `json:"read_description,omitempty"`  // 云文档国际化读权限说明
	WriteDescription string `json:"write_description,omitempty"` // 云文档国际化写权限说明
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityDocsBlock ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityDocsBlock struct {
	BlockTypeID   string                                                                                        `json:"block_type_id,omitempty"`   // BlockTypeID
	I18n          []*EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityDocsBlockI18n `json:"i18n,omitempty"`            // block 的国际化信息, 最小长度：`1`
	MobileIconURL string                                                                                        `json:"mobile_icon_url,omitempty"` // 移动端 icon 链接
	PcIconURL     string                                                                                        `json:"pc_icon_url,omitempty"`     // pc 端口 icon 链接
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityDocsBlockI18n ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityDocsBlockI18n struct {
	I18nKey string `json:"i18n_key,omitempty"` // 国际化语言的 key, 可选值有: `zh_cn`：中文, `en_us`：英文, `ja_jp`：日文
	Name    string `json:"name,omitempty"`     // 名称, 最小长度：`1` 字符
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityMessageAction ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityMessageAction struct {
	PcAppLink     string                                                                                            `json:"pc_app_link,omitempty"`     // pc 端链接
	MobileAppLink string                                                                                            `json:"mobile_app_link,omitempty"` // 移动端链接
	I18n          []*EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityMessageActionI18n `json:"i18n,omitempty"`            // 国际化信息, 最小长度：`1`
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityMessageActionI18n ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityMessageActionI18n struct {
	I18nKey string `json:"i18n_key,omitempty"` // 国际化语言的 key, 可选值有: `zh_cn`：中文, `en_us`：英文, `ja_jp`：日文
	Name    string `json:"name,omitempty"`     // 国际化名称, 最小长度：`1` 字符
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityPlusMenu ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionAbilityPlusMenu struct {
	PcAppLink     string `json:"pc_app_link,omitempty"`     // pc 端链接
	MobileAppLink string `json:"mobile_app_link,omitempty"` // 移动端链接
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemark ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemark struct {
	Remark       string                                                                                  `json:"remark,omitempty"`        // 备注说明
	UpdateRemark string                                                                                  `json:"update_remark,omitempty"` // 更新说明
	Visibility   *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibility `json:"visibility,omitempty"`    // 可见性名单
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibility ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibility struct {
	IsAll         bool                                                                                                 `json:"is_all,omitempty"`         // 是否全员可见
	VisibleList   *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityVisibleList   `json:"visible_list,omitempty"`   // 可见名单
	InvisibleList *EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityInvisibleList `json:"invisible_list,omitempty"` // 不可见名单
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityVisibleList ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityVisibleList struct {
	OpenIDs       []*EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityVisibleListOpenID `json:"open_ids,omitempty"`       // 可见性成员 id 列表
	DepartmentIDs []string                                                                                                   `json:"department_ids,omitempty"` // 可见性部门的 id 列表
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityVisibleListOpenID ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityVisibleListOpenID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityInvisibleList ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityInvisibleList struct {
	OpenIDs       []*EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityInvisibleListOpenID `json:"open_ids,omitempty"`       // 可见性成员 id 列表
	DepartmentIDs []string                                                                                                     `json:"department_ids,omitempty"` // 可见性部门的 id 列表
}

// EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityInvisibleListOpenID ...
type EventV2ApplicationApplicationAppVersionPublishApplyV6UnderAuditVersionRemarkVisibilityInvisibleListOpenID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ApplicationApplicationAppVersionPublishRevokeV6
//
// 通过订阅该事件，可接收应用撤回发布申请事件
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/application-app_version/events/publish_revoke
func (r *EventCallbackService) HandlerEventV2ApplicationApplicationAppVersionPublishRevokeV6(f EventV2ApplicationApplicationAppVersionPublishRevokeV6Handler) {
	r.cli.eventHandler.eventV2ApplicationApplicationAppVersionPublishRevokeV6Handler = f
}

// EventV2ApplicationApplicationAppVersionPublishRevokeV6Handler event EventV2ApplicationApplicationAppVersionPublishRevokeV6 handler
type EventV2ApplicationApplicationAppVersionPublishRevokeV6Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ApplicationApplicationAppVersionPublishRevokeV6) (string, error)

// EventV2ApplicationApplicationAppVersionPublishRevokeV6 ...
type EventV2ApplicationApplicationAppVersionPublishRevokeV6 struct {
	OperatorID *EventV2ApplicationApplicationAppVersionPublishRevokeV6OperatorID `json:"operator_id,omitempty"` // 用户 ID
	CreatorID  *EventV2ApplicationApplicationAppVersionPublishRevokeV6CreatorID  `json:"creator_id,omitempty"`  // 用户 ID
	AppID      string                                                            `json:"app_id,omitempty"`      // 撤回应用的 id
	VersionID  string                                                            `json:"version_id,omitempty"`  // 撤回应用的版本 id
}

// EventV2ApplicationApplicationAppVersionPublishRevokeV6OperatorID ...
type EventV2ApplicationApplicationAppVersionPublishRevokeV6OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2ApplicationApplicationAppVersionPublishRevokeV6CreatorID ...
type EventV2ApplicationApplicationAppVersionPublishRevokeV6CreatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ApplicationApplicationCreatedV6
//
// 当企业内有新的应用被创建时推送此事件{使用示例}(url=/api/tools/api_explore/api_explore_config?project=application&version=v6&resource=application&event=created)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/application/events/created
func (r *EventCallbackService) HandlerEventV2ApplicationApplicationCreatedV6(f EventV2ApplicationApplicationCreatedV6Handler) {
	r.cli.eventHandler.eventV2ApplicationApplicationCreatedV6Handler = f
}

// EventV2ApplicationApplicationCreatedV6Handler event EventV2ApplicationApplicationCreatedV6 handler
type EventV2ApplicationApplicationCreatedV6Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ApplicationApplicationCreatedV6) (string, error)

// EventV2ApplicationApplicationCreatedV6 ...
type EventV2ApplicationApplicationCreatedV6 struct {
	OperatorID      *EventV2ApplicationApplicationCreatedV6OperatorID `json:"operator_id,omitempty"`      // 用户 ID
	AppID           string                                            `json:"app_id,omitempty"`           // 应用 ID
	Name            string                                            `json:"name,omitempty"`             // 应用名称
	Description     string                                            `json:"description,omitempty"`      // 应用描述
	Avatar          string                                            `json:"avatar,omitempty"`           // 应用图标链接
	AppSceneType    int64                                             `json:"app_scene_type,omitempty"`   // 应用类型，0: 自建应用，1: 应用商店应用
	PrimaryLanguage string                                            `json:"primary_language,omitempty"` // 应用主语言
}

// EventV2ApplicationApplicationCreatedV6OperatorID ...
type EventV2ApplicationApplicationCreatedV6OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ApplicationApplicationFeedbackCreatedV6
//
// 当应用收到新反馈时，触发该事件{使用示例}(url=/api/tools/api_explore/api_explore_config?project=application&version=v6&resource=application.feedback&event=created)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/application-feedback/events/created
func (r *EventCallbackService) HandlerEventV2ApplicationApplicationFeedbackCreatedV6(f EventV2ApplicationApplicationFeedbackCreatedV6Handler) {
	r.cli.eventHandler.eventV2ApplicationApplicationFeedbackCreatedV6Handler = f
}

// EventV2ApplicationApplicationFeedbackCreatedV6Handler event EventV2ApplicationApplicationFeedbackCreatedV6 handler
type EventV2ApplicationApplicationFeedbackCreatedV6Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ApplicationApplicationFeedbackCreatedV6) (string, error)

// EventV2ApplicationApplicationFeedbackCreatedV6 ...
type EventV2ApplicationApplicationFeedbackCreatedV6 struct {
	UserID       *EventV2ApplicationApplicationFeedbackCreatedV6UserID `json:"user_id,omitempty"`       // 用户 ID
	AppID        string                                                `json:"app_id,omitempty"`        // 被反馈应用 ID
	FeedbackTime string                                                `json:"feedback_time,omitempty"` // 反馈提交时间，格式为yyyy-mm-dd hh:mm:ss
	TenantName   string                                                `json:"tenant_name,omitempty"`   // 反馈用户的租户名
	FeedbackType int64                                                 `json:"feedback_type,omitempty"` // 反馈类型（枚举值，1：故障反馈，2：产品建议）
	FaultType    []int64                                               `json:"fault_type,omitempty"`    // 故障类型列表：1: 黑屏 2: 白屏 3: 无法打开小程序  4: 卡顿 5: 小程序闪退 6: 页面加载慢 7: 死机 8: 其他异常
	FaultTime    string                                                `json:"fault_time,omitempty"`    // 故障时间，格式为yyyy-mm-dd hh:mm:ss
	Source       int64                                                 `json:"source,omitempty"`        // 反馈来源：1： 小程序 2：网页应用 3：机器人 4：webSDK
	Contact      string                                                `json:"contact,omitempty"`       // 用户填写的联系方式,**字段权限要求（满足任一）**：,获取用户邮箱信息,获取用户手机号
	Description  string                                                `json:"description,omitempty"`   // 反馈详情
	Images       []string                                              `json:"images,omitempty"`        // 反馈图片url列表，url 过期时间三天
	FeedbackID   string                                                `json:"feedback_id,omitempty"`   // 应用反馈 ID，应用反馈记录唯一标识
}

// EventV2ApplicationApplicationFeedbackCreatedV6UserID ...
type EventV2ApplicationApplicationFeedbackCreatedV6UserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ApplicationApplicationFeedbackUpdatedV6
//
// 当反馈的处理状态被更新时，触发该事件{使用示例}(url=/api/tools/api_explore/api_explore_config?project=application&version=v6&resource=application.feedback&event=updated)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/application-feedback/events/updated
func (r *EventCallbackService) HandlerEventV2ApplicationApplicationFeedbackUpdatedV6(f EventV2ApplicationApplicationFeedbackUpdatedV6Handler) {
	r.cli.eventHandler.eventV2ApplicationApplicationFeedbackUpdatedV6Handler = f
}

// EventV2ApplicationApplicationFeedbackUpdatedV6Handler event EventV2ApplicationApplicationFeedbackUpdatedV6 handler
type EventV2ApplicationApplicationFeedbackUpdatedV6Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ApplicationApplicationFeedbackUpdatedV6) (string, error)

// EventV2ApplicationApplicationFeedbackUpdatedV6 ...
type EventV2ApplicationApplicationFeedbackUpdatedV6 struct {
	FeedbackIDs []string                                                  `json:"feedback_ids,omitempty"` // 反馈id列表
	Status      int64                                                     `json:"status,omitempty"`       // 反馈处理状态（枚举值，0: 未处理  1: 已处理  2: 处理中 3: 已关闭）
	AppID       string                                                    `json:"app_id,omitempty"`       // 应用的 app_id
	UpdateTime  string                                                    `json:"update_time,omitempty"`  // 反馈处理时间，格式为yyyy-mm-dd hh:mm:ss
	OperatorID  *EventV2ApplicationApplicationFeedbackUpdatedV6OperatorID `json:"operator_id,omitempty"`  // 操作者用户 ID
}

// EventV2ApplicationApplicationFeedbackUpdatedV6OperatorID ...
type EventV2ApplicationApplicationFeedbackUpdatedV6OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ApplicationApplicationVisibilityAddedV6
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 仅当企业的用户通过「普通成员安装」方式获得应用可用性时推送此事件。
// - 订阅前提：需要是应用商店应用
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/application-v6/event/app-availability-scope-extended
func (r *EventCallbackService) HandlerEventV2ApplicationApplicationVisibilityAddedV6(f EventV2ApplicationApplicationVisibilityAddedV6Handler) {
	r.cli.eventHandler.eventV2ApplicationApplicationVisibilityAddedV6Handler = f
}

// EventV2ApplicationApplicationVisibilityAddedV6Handler event EventV2ApplicationApplicationVisibilityAddedV6 handler
type EventV2ApplicationApplicationVisibilityAddedV6Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ApplicationApplicationVisibilityAddedV6) (string, error)

// EventV2ApplicationApplicationVisibilityAddedV6 ...
type EventV2ApplicationApplicationVisibilityAddedV6 struct {
	Source int64                                                 `json:"source,omitempty"` // 事件来源, 为 1 时代表通过普通成员安装增加可见性. 如: 1
	Users  []*EventV2ApplicationApplicationVisibilityAddedV6User `json:"users,omitempty"`
}

// EventV2ApplicationApplicationVisibilityAddedV6User ...
type EventV2ApplicationApplicationVisibilityAddedV6User struct {
	UserID *EventV2ApplicationApplicationVisibilityAddedV6UserUserID `json:"user_id,omitempty"` // 开通的用户 id
}

// EventV2ApplicationApplicationVisibilityAddedV6UserUserID ...
type EventV2ApplicationApplicationVisibilityAddedV6UserUserID struct {
	OpenID  string `json:"open_id,omitempty"`  // 如: ou_f370aea2baf6ffc69a6762d31cfaf96a
	UnionID string `json:"union_id,omitempty"` // 如: on_6bc1e6c0e9ad8193fa4391278eb76891
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ApprovalApprovalUpdatedV4
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 「审批」定义更新时触发此事件
// * 依赖权限：[访问审批应用]
// **回调示例：**
// ```json
// {
// "schema": "2.0",
// "header": {
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uIDO24iM4YjLygjN/event/custom-approval-event
func (r *EventCallbackService) HandlerEventV2ApprovalApprovalUpdatedV4(f EventV2ApprovalApprovalUpdatedV4Handler) {
	r.cli.eventHandler.eventV2ApprovalApprovalUpdatedV4Handler = f
}

// EventV2ApprovalApprovalUpdatedV4Handler event EventV2ApprovalApprovalUpdatedV4 handler
type EventV2ApprovalApprovalUpdatedV4Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ApprovalApprovalUpdatedV4) (string, error)

// EventV2ApprovalApprovalUpdatedV4 ...
type EventV2ApprovalApprovalUpdatedV4 struct {
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2AttendanceUserFlowCreatedV1
//
// 为了更好地提升接口文档的的易理解性，我们对文档进行了升级，请尽快迁移至[新版本>>](https://open.feishu.cn/document/ukTMukTMukTM/uYDNxYjL2QTM24iN0EjN/event-list)
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// **事件**
// 用户打卡成功后，推送该用户的打卡流水消息。（不支持商店应用使用）
// **事件体**
// |名称|类型|描述|
// |---|---|---|
// |schema|string|事件模式|
// |header|event_header|事件头|
// |&emsp;∟event_id|string|事件 ID|
// |&emsp;∟event_type|string|事件类型|
// |&emsp;∟create_time|string|事件创建时间戳（单位：毫秒）|
// |&emsp;∟token|string|事件 Token|
// |&emsp;∟app_id|string|应用 ID|
// |&emsp;∟tenant_key|string|租户 Key|
// |event|-|事件体|
// |&emsp;∟employee_id|string|员工 ID|
// |&emsp;∟employee_no|string|员工工号|
// |&emsp;∟location_name|string|打卡位置名称信息|
// |&emsp;∟check_time|string|打卡时间，精确到秒的时间戳|
// |&emsp;∟comment|string|打卡备注|
// |&emsp;∟record_id|string|打卡记录 ID|
// |&emsp;∟longitude|float|打卡经度|
// |&emsp;∟latitude|float|打卡纬度|
// |&emsp;∟ssid|string|打卡 Wi-Fi 的 SSID|
// |&emsp;∟bssid|string|打卡 Wi-Fi 的 MAC 地址|
// |&emsp;∟is_field|boolean|是否为外勤打卡|
// |&emsp;∟is_wifi|boolean|是否为 Wi-Fi 打卡|
// |&emsp;∟type|int|记录生成方式，可用值：【0（用户自己打卡），1（管理员修改），2（用户补卡），3（系统自动生成），4（下班免打卡），5（考勤机打卡），6（极速打卡），7（考勤开放平台导入）】|
// |&emsp;∟photo_urls|string\[\]|打卡照片列表|
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/Attendance/event/user-attendance-records-event
func (r *EventCallbackService) HandlerEventV2AttendanceUserFlowCreatedV1(f EventV2AttendanceUserFlowCreatedV1Handler) {
	r.cli.eventHandler.eventV2AttendanceUserFlowCreatedV1Handler = f
}

// EventV2AttendanceUserFlowCreatedV1Handler event EventV2AttendanceUserFlowCreatedV1 handler
type EventV2AttendanceUserFlowCreatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2AttendanceUserFlowCreatedV1) (string, error)

// EventV2AttendanceUserFlowCreatedV1 ...
type EventV2AttendanceUserFlowCreatedV1 struct {
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2AttendanceUserTaskUpdatedV1
//
// 为了更好地提升接口文档的的易理解性，我们对文档进行了升级，请尽快迁移至[新版本>>](https://open.feishu.cn/document/ukTMukTMukTM/uYDNxYjL2QTM24iN0EjN/event-list)
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// **事件**
// 当用户任务变更后，推送该用户的任务状态变更消息。（不支持商店应用）
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/Attendance/event/user-task-status-change-event
func (r *EventCallbackService) HandlerEventV2AttendanceUserTaskUpdatedV1(f EventV2AttendanceUserTaskUpdatedV1Handler) {
	r.cli.eventHandler.eventV2AttendanceUserTaskUpdatedV1Handler = f
}

// EventV2AttendanceUserTaskUpdatedV1Handler event EventV2AttendanceUserTaskUpdatedV1 handler
type EventV2AttendanceUserTaskUpdatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2AttendanceUserTaskUpdatedV1) (string, error)

// EventV2AttendanceUserTaskUpdatedV1 ...
type EventV2AttendanceUserTaskUpdatedV1 struct {
	EmployeeID    string                                            `json:"employee_id,omitempty"`    // 员工 ID
	EmployeeNo    string                                            `json:"employee_no,omitempty"`    // 员工工号
	GroupID       string                                            `json:"group_id,omitempty"`       // 考勤组 ID
	ShiftID       string                                            `json:"shift_id,omitempty"`       // 班次 ID
	Date          int64                                             `json:"date,omitempty"`           // 日期
	StatusChanges []*EventV2AttendanceUserTaskUpdatedV1StatusChange `json:"status_changes,omitempty"` // 状态变更数组
}

// EventV2AttendanceUserTaskUpdatedV1StatusChange ...
type EventV2AttendanceUserTaskUpdatedV1StatusChange struct {
	BeforeStatus      string `json:"before_status,omitempty"`      // 变更前打卡结果，值为：【NoNeedCheck（无需打卡），SystemCheck（系统打卡），Normal（正常），Early（早退），Late（迟到），Lack（缺卡）】
	CurrentStatus     string `json:"current_status,omitempty"`     // 变更后打卡结果，值为：【NoNeedCheck（无需打卡），SystemCheck（系统打卡），Normal（正常），Early（早退），Late（迟到），Lack（缺卡）】
	BeforeSupplement  string `json:"before_supplement,omitempty"`  // 变更前结果补充，值为：【None（无），ManagerModification（管理员修改），CardReplacement（补卡通过），ShiftChange（换班），Travel（出差），Leave（请假），GoOut（外出），CardReplacementApplication（补卡申请中），FieldPunch（外勤打卡）】
	CurrentSupplement string `json:"current_supplement,omitempty"` // 变更后打卡结果补充，值为：【None（无），ManagerModification（管理员修改），CardReplacement（补卡通过），ShiftChange（换班），Travel（出差），Leave（请假），GoOut（外出），CardReplacementApplication（补卡申请中），FieldPunch（外勤打卡）】
	WorkType          string `json:"work_type,omitempty"`          // 上下班状态变更，值为：【on（上班），off（下班）】
	Index             string `json:"index,omitempty"`              // 任务中的第几次上下班
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2AwemeEcosystemAwemeUserBindedAccountV1
//
// 绑定关系变更事件
// 适用于获取“抖音员工号”运营者是否发生变更，即当“抖音员工号”关联的飞书账号发生变化时，可通过该权限获知到此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=aweme_ecosystem&version=v1&resource=aweme_user&event=binded_account)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bytedance-open-ecosystem/aweme_ecosystem-v1/aweme_user/events/binded_account
func (r *EventCallbackService) HandlerEventV2AwemeEcosystemAwemeUserBindedAccountV1(f EventV2AwemeEcosystemAwemeUserBindedAccountV1Handler) {
	r.cli.eventHandler.eventV2AwemeEcosystemAwemeUserBindedAccountV1Handler = f
}

// EventV2AwemeEcosystemAwemeUserBindedAccountV1Handler event EventV2AwemeEcosystemAwemeUserBindedAccountV1 handler
type EventV2AwemeEcosystemAwemeUserBindedAccountV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2AwemeEcosystemAwemeUserBindedAccountV1) (string, error)

// EventV2AwemeEcosystemAwemeUserBindedAccountV1 ...
type EventV2AwemeEcosystemAwemeUserBindedAccountV1 struct {
	EventAwemeUser *EventV2AwemeEcosystemAwemeUserBindedAccountV1EventAwemeUser `json:"event_aweme_user,omitempty"` // 变更后绑定关系
}

// EventV2AwemeEcosystemAwemeUserBindedAccountV1EventAwemeUser ...
type EventV2AwemeEcosystemAwemeUserBindedAccountV1EventAwemeUser struct {
	UserID      *EventV2AwemeEcosystemAwemeUserBindedAccountV1EventAwemeUserUserID `json:"user_id,omitempty"`       // 用户 ID
	AwemeUserID string                                                             `json:"aweme_user_id,omitempty"` // 绑定的抖音用户ID
	IsBinded    bool                                                               `json:"is_binded,omitempty"`     // 飞书账号是否有绑定关系
}

// EventV2AwemeEcosystemAwemeUserBindedAccountV1EventAwemeUserUserID ...
type EventV2AwemeEcosystemAwemeUserBindedAccountV1EventAwemeUserUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2CalendarCalendarACLCreatedV4
//
// 当被订阅的日历上有ACL被创建时触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=calendar&version=v4&resource=calendar.acl&event=created)
// 特殊说明：应用首先需要调用上述接口建立订阅关系。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-acl/events/created
func (r *EventCallbackService) HandlerEventV2CalendarCalendarACLCreatedV4(f EventV2CalendarCalendarACLCreatedV4Handler) {
	r.cli.eventHandler.eventV2CalendarCalendarACLCreatedV4Handler = f
}

// EventV2CalendarCalendarACLCreatedV4Handler event EventV2CalendarCalendarACLCreatedV4 handler
type EventV2CalendarCalendarACLCreatedV4Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2CalendarCalendarACLCreatedV4) (string, error)

// EventV2CalendarCalendarACLCreatedV4 ...
type EventV2CalendarCalendarACLCreatedV4 struct {
	ACLID      string                                       `json:"acl_id,omitempty"`       // acl资源ID
	Role       CalendarRole                                 `json:"role,omitempty"`         // 对日历的访问权限, 可选值有: `unknown`：未知权限, `free_busy_reader`：游客，只能看到忙碌/空闲信息, `reader`：订阅者，查看所有日程详情, `writer`：编辑者，创建及修改日程, `owner`：管理员，管理日历及共享设置
	Scope      *EventV2CalendarCalendarACLCreatedV4Scope    `json:"scope,omitempty"`        // 权限范围
	UserIDList []*EventV2CalendarCalendarACLCreatedV4UserID `json:"user_id_list,omitempty"` // 需要推送事件的用户列表
}

// EventV2CalendarCalendarACLCreatedV4Scope ...
type EventV2CalendarCalendarACLCreatedV4Scope struct {
	Type   string                                          `json:"type,omitempty"`    // 权限类型，当type为User时，值为open_id/user_id/union_id, 可选值有: `user`：用户
	UserID *EventV2CalendarCalendarACLCreatedV4ScopeUserID `json:"user_id,omitempty"` // 用户 ID
}

// EventV2CalendarCalendarACLCreatedV4ScopeUserID ...
type EventV2CalendarCalendarACLCreatedV4ScopeUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2CalendarCalendarACLCreatedV4UserID ...
type EventV2CalendarCalendarACLCreatedV4UserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2CalendarCalendarACLDeletedV4
//
// 当被订阅的日历上有ACL被删除时触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=calendar&version=v4&resource=calendar.acl&event=deleted)
// 特殊说明：应用首先需要调用上述接口建立订阅关系。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-acl/events/deleted
func (r *EventCallbackService) HandlerEventV2CalendarCalendarACLDeletedV4(f EventV2CalendarCalendarACLDeletedV4Handler) {
	r.cli.eventHandler.eventV2CalendarCalendarACLDeletedV4Handler = f
}

// EventV2CalendarCalendarACLDeletedV4Handler event EventV2CalendarCalendarACLDeletedV4 handler
type EventV2CalendarCalendarACLDeletedV4Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2CalendarCalendarACLDeletedV4) (string, error)

// EventV2CalendarCalendarACLDeletedV4 ...
type EventV2CalendarCalendarACLDeletedV4 struct {
	ACLID      string                                       `json:"acl_id,omitempty"`       // acl资源ID
	Role       CalendarRole                                 `json:"role,omitempty"`         // 对日历的访问权限, 可选值有: `unknown`：未知权限, `free_busy_reader`：游客，只能看到忙碌/空闲信息, `reader`：订阅者，查看所有日程详情, `writer`：编辑者，创建及修改日程, `owner`：管理员，管理日历及共享设置
	Scope      *EventV2CalendarCalendarACLDeletedV4Scope    `json:"scope,omitempty"`        // 权限范围
	UserIDList []*EventV2CalendarCalendarACLDeletedV4UserID `json:"user_id_list,omitempty"` // 需要推送事件的用户列表
}

// EventV2CalendarCalendarACLDeletedV4Scope ...
type EventV2CalendarCalendarACLDeletedV4Scope struct {
	Type   string                                          `json:"type,omitempty"`    // 权限类型，当type为User时，值为open_id/user_id/union_id, 可选值有: `user`：用户
	UserID *EventV2CalendarCalendarACLDeletedV4ScopeUserID `json:"user_id,omitempty"` // 用户 ID
}

// EventV2CalendarCalendarACLDeletedV4ScopeUserID ...
type EventV2CalendarCalendarACLDeletedV4ScopeUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求:  获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2CalendarCalendarACLDeletedV4UserID ...
type EventV2CalendarCalendarACLDeletedV4UserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2CalendarCalendarChangedV4
//
// 当订阅用户的日历列表有日历变动时触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=calendar&version=v4&resource=calendar&event=changed)
// 应用首先需要调用上述接口建立订阅关系。应用收到该事件后，使用事件的 user_list 字段中的用户对应的 user_access_token 调用[获取日历列表](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/list)接口拉取增量的变更数据
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar/events/changed
func (r *EventCallbackService) HandlerEventV2CalendarCalendarChangedV4(f EventV2CalendarCalendarChangedV4Handler) {
	r.cli.eventHandler.eventV2CalendarCalendarChangedV4Handler = f
}

// EventV2CalendarCalendarChangedV4Handler event EventV2CalendarCalendarChangedV4 handler
type EventV2CalendarCalendarChangedV4Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2CalendarCalendarChangedV4) (string, error)

// EventV2CalendarCalendarChangedV4 ...
type EventV2CalendarCalendarChangedV4 struct {
	UserIDList []*EventV2CalendarCalendarChangedV4UserID `json:"user_id_list,omitempty"` // 需要推送事件的用户列表
}

// EventV2CalendarCalendarChangedV4UserID ...
type EventV2CalendarCalendarChangedV4UserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2CalendarCalendarEventChangedV4
//
// 当被订阅的用户日历下有日程变更时触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=calendar&version=v4&resource=calendar.event&event=changed)
// 应用首先需要调用[订阅日程变更事件接口](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/subscription)建立订阅关系。应用收到该事件后，使用事件的 user_list 字段中的用户对应的 user_access_token 调用[获取日程列表](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/list)接口拉取事件中 calendar_id 字段对应的日历下的日程数据
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/calendar-v4/calendar-event/events/changed
func (r *EventCallbackService) HandlerEventV2CalendarCalendarEventChangedV4(f EventV2CalendarCalendarEventChangedV4Handler) {
	r.cli.eventHandler.eventV2CalendarCalendarEventChangedV4Handler = f
}

// EventV2CalendarCalendarEventChangedV4Handler event EventV2CalendarCalendarEventChangedV4 handler
type EventV2CalendarCalendarEventChangedV4Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2CalendarCalendarEventChangedV4) (string, error)

// EventV2CalendarCalendarEventChangedV4 ...
type EventV2CalendarCalendarEventChangedV4 struct {
	CalendarID string                                         `json:"calendar_id,omitempty"`  // 日历id
	UserIDList []*EventV2CalendarCalendarEventChangedV4UserID `json:"user_id_list,omitempty"` // 需要推送事件的用户列表
}

// EventV2CalendarCalendarEventChangedV4UserID ...
type EventV2CalendarCalendarEventChangedV4UserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactCustomAttrEventUpdatedV3
//
// 通过该事件订阅成员字段变更。old_object 展示更新字段的原始值。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=custom_attr_event&event=updated)
// 触发事件的动作有「打开/关闭」开关、「增加/删除」成员字段。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/custom_attr_event/events/updated
func (r *EventCallbackService) HandlerEventV2ContactCustomAttrEventUpdatedV3(f EventV2ContactCustomAttrEventUpdatedV3Handler) {
	r.cli.eventHandler.eventV2ContactCustomAttrEventUpdatedV3Handler = f
}

// EventV2ContactCustomAttrEventUpdatedV3Handler event EventV2ContactCustomAttrEventUpdatedV3 handler
type EventV2ContactCustomAttrEventUpdatedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactCustomAttrEventUpdatedV3) (string, error)

// EventV2ContactCustomAttrEventUpdatedV3 ...
type EventV2ContactCustomAttrEventUpdatedV3 struct {
	Object    *EventV2ContactCustomAttrEventUpdatedV3Object    `json:"object,omitempty"`     // 变更后信息
	OldObject *EventV2ContactCustomAttrEventUpdatedV3OldObject `json:"old_object,omitempty"` // 变更前信息
}

// EventV2ContactCustomAttrEventUpdatedV3Object ...
type EventV2ContactCustomAttrEventUpdatedV3Object struct {
	ContactFieldKey []string `json:"contact_field_key,omitempty"` // 通讯录字段键值
	AllowOpenQuery  bool     `json:"allow_open_query,omitempty"`  // 开关是否打开
}

// EventV2ContactCustomAttrEventUpdatedV3OldObject ...
type EventV2ContactCustomAttrEventUpdatedV3OldObject struct {
	ContactFieldKey []string `json:"contact_field_key,omitempty"` // 通讯录字段键值
	AllowOpenQuery  bool     `json:"allow_open_query,omitempty"`  // 开关是否打开
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactDepartmentCreatedV3
//
// 创建通讯录部门时发送该事件给订阅应用。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=department&event=created)
// 只有当应用拥有被改动字段的数据权限时，才会接收到事件。具体的数据权限与字段的关系请参考[应用权限](https://open.feishu.cn/document/ukTMukTMukTM/uQjN3QjL0YzN04CN2cDN)，或查看事件体参数列表的字段描述。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/events/created
func (r *EventCallbackService) HandlerEventV2ContactDepartmentCreatedV3(f EventV2ContactDepartmentCreatedV3Handler) {
	r.cli.eventHandler.eventV2ContactDepartmentCreatedV3Handler = f
}

// EventV2ContactDepartmentCreatedV3Handler event EventV2ContactDepartmentCreatedV3 handler
type EventV2ContactDepartmentCreatedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactDepartmentCreatedV3) (string, error)

// EventV2ContactDepartmentCreatedV3 ...
type EventV2ContactDepartmentCreatedV3 struct {
	Object *EventV2ContactDepartmentCreatedV3Object `json:"object,omitempty"` // 部门信息
}

// EventV2ContactDepartmentCreatedV3Object ...
type EventV2ContactDepartmentCreatedV3Object struct {
	Name               string                                         `json:"name,omitempty"`                 // 部门名称, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	ParentDepartmentID string                                         `json:"parent_department_id,omitempty"` // 父部门的部门open_department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	DepartmentID       string                                         `json:"department_id,omitempty"`        // 本部门的department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	OpenDepartmentID   string                                         `json:"open_department_id,omitempty"`   // 部门的open_department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0)
	LeaderUserID       string                                         `json:"leader_user_id,omitempty"`       // 部门主管用户open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	ChatID             string                                         `json:"chat_id,omitempty"`              // 部门群ID,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	Order              int64                                          `json:"order,omitempty"`                // 部门的排序,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	Status             *EventV2ContactDepartmentCreatedV3ObjectStatus `json:"status,omitempty"`               // 部门状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
}

// EventV2ContactDepartmentCreatedV3ObjectStatus ...
type EventV2ContactDepartmentCreatedV3ObjectStatus struct {
	IsDeleted bool `json:"is_deleted,omitempty"` // 是否被删除
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactDepartmentDeletedV3
//
// 订阅这一事件可以获得被删除部门的信息。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=department&event=deleted)
// 只有当应用拥有被改动字段的数据权限时，才会接收到事件。具体的数据权限与字段的关系请参考[应用权限](https://open.feishu.cn/document/ukTMukTMukTM/uQjN3QjL0YzN04CN2cDN)，或查看事件体参数列表的字段描述。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/events/deleted
func (r *EventCallbackService) HandlerEventV2ContactDepartmentDeletedV3(f EventV2ContactDepartmentDeletedV3Handler) {
	r.cli.eventHandler.eventV2ContactDepartmentDeletedV3Handler = f
}

// EventV2ContactDepartmentDeletedV3Handler event EventV2ContactDepartmentDeletedV3 handler
type EventV2ContactDepartmentDeletedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactDepartmentDeletedV3) (string, error)

// EventV2ContactDepartmentDeletedV3 ...
type EventV2ContactDepartmentDeletedV3 struct {
	Object    *EventV2ContactDepartmentDeletedV3Object    `json:"object,omitempty"`     // 部门信息
	OldObject *EventV2ContactDepartmentDeletedV3OldObject `json:"old_object,omitempty"` // 部门被删除前的信息
}

// EventV2ContactDepartmentDeletedV3Object ...
type EventV2ContactDepartmentDeletedV3Object struct {
	Name               string                                         `json:"name,omitempty"`                 // 部门名称, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	ParentDepartmentID string                                         `json:"parent_department_id,omitempty"` // 父部门的部门open_department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	DepartmentID       string                                         `json:"department_id,omitempty"`        // 本部门的department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	OpenDepartmentID   string                                         `json:"open_department_id,omitempty"`   // 部门的open_department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0)
	LeaderUserID       string                                         `json:"leader_user_id,omitempty"`       // 部门主管用户open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	ChatID             string                                         `json:"chat_id,omitempty"`              // 部门群ID,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	Order              int64                                          `json:"order,omitempty"`                // 部门的排序,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	Status             *EventV2ContactDepartmentDeletedV3ObjectStatus `json:"status,omitempty"`               // 部门状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
}

// EventV2ContactDepartmentDeletedV3ObjectStatus ...
type EventV2ContactDepartmentDeletedV3ObjectStatus struct {
	IsDeleted bool `json:"is_deleted,omitempty"` // 是否被删除
}

// EventV2ContactDepartmentDeletedV3OldObject ...
type EventV2ContactDepartmentDeletedV3OldObject struct {
	Status           *EventV2ContactDepartmentDeletedV3OldObjectStatus `json:"status,omitempty"`             // 部门状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	OpenDepartmentID string                                            `json:"open_department_id,omitempty"` // 部门open_id
}

// EventV2ContactDepartmentDeletedV3OldObjectStatus ...
type EventV2ContactDepartmentDeletedV3OldObjectStatus struct {
	IsDeleted bool `json:"is_deleted,omitempty"` // 是否被删除
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactDepartmentUpdatedV3
//
// 通过该事件订阅部门更新。old_object只展示被更新字段的原始值。应用身份访问通讯录的权限为历史版本，不推荐申请。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=department&event=updated)
// 只有当应用拥有被改动字段的数据权限时，才会接收到事件。具体的数据权限与字段的关系请参考[应用权限](https://open.feishu.cn/document/ukTMukTMukTM/uQjN3QjL0YzN04CN2cDN)，或查看事件体参数列表的字段描述。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/events/updated
func (r *EventCallbackService) HandlerEventV2ContactDepartmentUpdatedV3(f EventV2ContactDepartmentUpdatedV3Handler) {
	r.cli.eventHandler.eventV2ContactDepartmentUpdatedV3Handler = f
}

// EventV2ContactDepartmentUpdatedV3Handler event EventV2ContactDepartmentUpdatedV3 handler
type EventV2ContactDepartmentUpdatedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactDepartmentUpdatedV3) (string, error)

// EventV2ContactDepartmentUpdatedV3 ...
type EventV2ContactDepartmentUpdatedV3 struct {
	Object    *EventV2ContactDepartmentUpdatedV3Object    `json:"object,omitempty"`     // 更新后信息
	OldObject *EventV2ContactDepartmentUpdatedV3OldObject `json:"old_object,omitempty"` // 更新前信息
}

// EventV2ContactDepartmentUpdatedV3Object ...
type EventV2ContactDepartmentUpdatedV3Object struct {
	Name               string                                         `json:"name,omitempty"`                 // 部门名称, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	ParentDepartmentID string                                         `json:"parent_department_id,omitempty"` // 父部门的部门open_department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	DepartmentID       string                                         `json:"department_id,omitempty"`        // 本部门的department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	OpenDepartmentID   string                                         `json:"open_department_id,omitempty"`   // 部门的open_department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0)
	LeaderUserID       string                                         `json:"leader_user_id,omitempty"`       // 部门主管用户open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	ChatID             string                                         `json:"chat_id,omitempty"`              // 部门群ID,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	Order              int64                                          `json:"order,omitempty"`                // 部门的排序,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	Status             *EventV2ContactDepartmentUpdatedV3ObjectStatus `json:"status,omitempty"`               // 部门状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
}

// EventV2ContactDepartmentUpdatedV3ObjectStatus ...
type EventV2ContactDepartmentUpdatedV3ObjectStatus struct {
	IsDeleted bool `json:"is_deleted,omitempty"` // 是否被删除
}

// EventV2ContactDepartmentUpdatedV3OldObject ...
type EventV2ContactDepartmentUpdatedV3OldObject struct {
	Name               string                                            `json:"name,omitempty"`                 // 部门名称, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	ParentDepartmentID string                                            `json:"parent_department_id,omitempty"` // 父部门的部门open_department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	DepartmentID       string                                            `json:"department_id,omitempty"`        // 本部门的department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	OpenDepartmentID   string                                            `json:"open_department_id,omitempty"`   // 部门的open_department_id [部门相关ID概念](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview#23857fe0)
	LeaderUserID       string                                            `json:"leader_user_id,omitempty"`       // 部门主管用户open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
	ChatID             string                                            `json:"chat_id,omitempty"`              // 部门群ID,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	Order              int64                                             `json:"order,omitempty"`                // 部门的排序,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,读取通讯录,以应用身份访问通讯录
	Status             *EventV2ContactDepartmentUpdatedV3OldObjectStatus `json:"status,omitempty"`               // 部门状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,读取通讯录,以应用身份访问通讯录
}

// EventV2ContactDepartmentUpdatedV3OldObjectStatus ...
type EventV2ContactDepartmentUpdatedV3OldObjectStatus struct {
	IsDeleted bool `json:"is_deleted,omitempty"` // 是否被删除
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactEmployeeTypeEnumActivedV3
//
// 启用人员类型会发出对应事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=employee_type_enum&event=actived)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/employee_type_enum/events/actived
func (r *EventCallbackService) HandlerEventV2ContactEmployeeTypeEnumActivedV3(f EventV2ContactEmployeeTypeEnumActivedV3Handler) {
	r.cli.eventHandler.eventV2ContactEmployeeTypeEnumActivedV3Handler = f
}

// EventV2ContactEmployeeTypeEnumActivedV3Handler event EventV2ContactEmployeeTypeEnumActivedV3 handler
type EventV2ContactEmployeeTypeEnumActivedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactEmployeeTypeEnumActivedV3) (string, error)

// EventV2ContactEmployeeTypeEnumActivedV3 ...
type EventV2ContactEmployeeTypeEnumActivedV3 struct {
	OldEnum *EventV2ContactEmployeeTypeEnumActivedV3OldEnum `json:"old_enum,omitempty"` // 旧枚举类型
	NewEnum *EventV2ContactEmployeeTypeEnumActivedV3NewEnum `json:"new_enum,omitempty"` // 新枚举类型
}

// EventV2ContactEmployeeTypeEnumActivedV3OldEnum ...
type EventV2ContactEmployeeTypeEnumActivedV3OldEnum struct {
	EnumID      string                                                       `json:"enum_id,omitempty"`      // 枚举值id
	EnumValue   string                                                       `json:"enum_value,omitempty"`   // 枚举的编号值，创建新的人员类型后，系统生成对应编号。对应[创建用户接口](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/create)中用户信息的employee_type字段值
	Content     string                                                       `json:"content,omitempty"`      // 枚举内容, 长度范围：`1` ～ `100` 字符
	EnumType    int64                                                        `json:"enum_type,omitempty"`    // 类型, 可选值有: `1`：内置类型, `2`：自定义
	EnumStatus  int64                                                        `json:"enum_status,omitempty"`  // 使用状态, 可选值有: `1`：激活, `2`：未激活
	I18nContent []*EventV2ContactEmployeeTypeEnumActivedV3OldEnumI18nContent `json:"i18n_content,omitempty"` // i18n定义
}

// EventV2ContactEmployeeTypeEnumActivedV3OldEnumI18nContent ...
type EventV2ContactEmployeeTypeEnumActivedV3OldEnumI18nContent struct {
	Locale string `json:"locale,omitempty"` // 语言版本
	Value  string `json:"value,omitempty"`  // 字段名
}

// EventV2ContactEmployeeTypeEnumActivedV3NewEnum ...
type EventV2ContactEmployeeTypeEnumActivedV3NewEnum struct {
	EnumID      string                                                       `json:"enum_id,omitempty"`      // 枚举值id
	EnumValue   string                                                       `json:"enum_value,omitempty"`   // 枚举的编号值，创建新的人员类型后，系统生成对应编号。对应[创建用户接口](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/create)中用户信息的employee_type字段值
	Content     string                                                       `json:"content,omitempty"`      // 枚举内容, 长度范围：`1` ～ `100` 字符
	EnumType    int64                                                        `json:"enum_type,omitempty"`    // 类型, 可选值有: `1`：内置类型, `2`：自定义
	EnumStatus  int64                                                        `json:"enum_status,omitempty"`  // 使用状态, 可选值有: `1`：激活, `2`：未激活
	I18nContent []*EventV2ContactEmployeeTypeEnumActivedV3NewEnumI18nContent `json:"i18n_content,omitempty"` // i18n定义
}

// EventV2ContactEmployeeTypeEnumActivedV3NewEnumI18nContent ...
type EventV2ContactEmployeeTypeEnumActivedV3NewEnumI18nContent struct {
	Locale string `json:"locale,omitempty"` // 语言版本
	Value  string `json:"value,omitempty"`  // 字段名
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactEmployeeTypeEnumCreatedV3
//
// 新建人员类型会发出对应事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=employee_type_enum&event=created)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/employee_type_enum/events/created
func (r *EventCallbackService) HandlerEventV2ContactEmployeeTypeEnumCreatedV3(f EventV2ContactEmployeeTypeEnumCreatedV3Handler) {
	r.cli.eventHandler.eventV2ContactEmployeeTypeEnumCreatedV3Handler = f
}

// EventV2ContactEmployeeTypeEnumCreatedV3Handler event EventV2ContactEmployeeTypeEnumCreatedV3 handler
type EventV2ContactEmployeeTypeEnumCreatedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactEmployeeTypeEnumCreatedV3) (string, error)

// EventV2ContactEmployeeTypeEnumCreatedV3 ...
type EventV2ContactEmployeeTypeEnumCreatedV3 struct {
	NewEnum *EventV2ContactEmployeeTypeEnumCreatedV3NewEnum `json:"new_enum,omitempty"` // 新枚举类型
}

// EventV2ContactEmployeeTypeEnumCreatedV3NewEnum ...
type EventV2ContactEmployeeTypeEnumCreatedV3NewEnum struct {
	EnumID      string                                                       `json:"enum_id,omitempty"`      // 枚举值id
	EnumValue   string                                                       `json:"enum_value,omitempty"`   // 枚举的编号值，创建新的人员类型后，系统生成对应编号。对应[创建用户接口](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/create)中用户信息的employee_type字段值
	Content     string                                                       `json:"content,omitempty"`      // 枚举内容, 长度范围：`1` ～ `100` 字符
	EnumType    int64                                                        `json:"enum_type,omitempty"`    // 类型, 可选值有: `1`：内置类型, `2`：自定义
	EnumStatus  int64                                                        `json:"enum_status,omitempty"`  // 使用状态, 可选值有: `1`：激活, `2`：未激活
	I18nContent []*EventV2ContactEmployeeTypeEnumCreatedV3NewEnumI18nContent `json:"i18n_content,omitempty"` // i18n定义
}

// EventV2ContactEmployeeTypeEnumCreatedV3NewEnumI18nContent ...
type EventV2ContactEmployeeTypeEnumCreatedV3NewEnumI18nContent struct {
	Locale string `json:"locale,omitempty"` // 语言版本
	Value  string `json:"value,omitempty"`  // 字段名
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactEmployeeTypeEnumDeactivatedV3
//
// 停用人员类型会发出对应事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=employee_type_enum&event=deactivated)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/employee_type_enum/events/deactivated
func (r *EventCallbackService) HandlerEventV2ContactEmployeeTypeEnumDeactivatedV3(f EventV2ContactEmployeeTypeEnumDeactivatedV3Handler) {
	r.cli.eventHandler.eventV2ContactEmployeeTypeEnumDeactivatedV3Handler = f
}

// EventV2ContactEmployeeTypeEnumDeactivatedV3Handler event EventV2ContactEmployeeTypeEnumDeactivatedV3 handler
type EventV2ContactEmployeeTypeEnumDeactivatedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactEmployeeTypeEnumDeactivatedV3) (string, error)

// EventV2ContactEmployeeTypeEnumDeactivatedV3 ...
type EventV2ContactEmployeeTypeEnumDeactivatedV3 struct {
	OldEnum *EventV2ContactEmployeeTypeEnumDeactivatedV3OldEnum `json:"old_enum,omitempty"` // 旧枚举类型
	NewEnum *EventV2ContactEmployeeTypeEnumDeactivatedV3NewEnum `json:"new_enum,omitempty"` // 新枚举类型
}

// EventV2ContactEmployeeTypeEnumDeactivatedV3OldEnum ...
type EventV2ContactEmployeeTypeEnumDeactivatedV3OldEnum struct {
	EnumID      string                                                           `json:"enum_id,omitempty"`      // 枚举值id
	EnumValue   string                                                           `json:"enum_value,omitempty"`   // 枚举的编号值，创建新的人员类型后，系统生成对应编号。对应[创建用户接口](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/create)中用户信息的employee_type字段值
	Content     string                                                           `json:"content,omitempty"`      // 枚举内容, 长度范围：`1` ～ `100` 字符
	EnumType    int64                                                            `json:"enum_type,omitempty"`    // 类型, 可选值有: `1`：内置类型, `2`：自定义
	EnumStatus  int64                                                            `json:"enum_status,omitempty"`  // 使用状态, 可选值有: `1`：激活, `2`：未激活
	I18nContent []*EventV2ContactEmployeeTypeEnumDeactivatedV3OldEnumI18nContent `json:"i18n_content,omitempty"` // i18n定义
}

// EventV2ContactEmployeeTypeEnumDeactivatedV3OldEnumI18nContent ...
type EventV2ContactEmployeeTypeEnumDeactivatedV3OldEnumI18nContent struct {
	Locale string `json:"locale,omitempty"` // 语言版本
	Value  string `json:"value,omitempty"`  // 字段名
}

// EventV2ContactEmployeeTypeEnumDeactivatedV3NewEnum ...
type EventV2ContactEmployeeTypeEnumDeactivatedV3NewEnum struct {
	EnumID      string                                                           `json:"enum_id,omitempty"`      // 枚举值id
	EnumValue   string                                                           `json:"enum_value,omitempty"`   // 枚举的编号值，创建新的人员类型后，系统生成对应编号。对应[创建用户接口](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/create)中用户信息的employee_type字段值
	Content     string                                                           `json:"content,omitempty"`      // 枚举内容, 长度范围：`1` ～ `100` 字符
	EnumType    int64                                                            `json:"enum_type,omitempty"`    // 类型, 可选值有: `1`：内置类型, `2`：自定义
	EnumStatus  int64                                                            `json:"enum_status,omitempty"`  // 使用状态, 可选值有: `1`：激活, `2`：未激活
	I18nContent []*EventV2ContactEmployeeTypeEnumDeactivatedV3NewEnumI18nContent `json:"i18n_content,omitempty"` // i18n定义
}

// EventV2ContactEmployeeTypeEnumDeactivatedV3NewEnumI18nContent ...
type EventV2ContactEmployeeTypeEnumDeactivatedV3NewEnumI18nContent struct {
	Locale string `json:"locale,omitempty"` // 语言版本
	Value  string `json:"value,omitempty"`  // 字段名
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactEmployeeTypeEnumDeletedV3
//
// 删除人员类型会发出对应事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=employee_type_enum&event=deleted)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/employee_type_enum/events/deleted
func (r *EventCallbackService) HandlerEventV2ContactEmployeeTypeEnumDeletedV3(f EventV2ContactEmployeeTypeEnumDeletedV3Handler) {
	r.cli.eventHandler.eventV2ContactEmployeeTypeEnumDeletedV3Handler = f
}

// EventV2ContactEmployeeTypeEnumDeletedV3Handler event EventV2ContactEmployeeTypeEnumDeletedV3 handler
type EventV2ContactEmployeeTypeEnumDeletedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactEmployeeTypeEnumDeletedV3) (string, error)

// EventV2ContactEmployeeTypeEnumDeletedV3 ...
type EventV2ContactEmployeeTypeEnumDeletedV3 struct {
	OldEnum *EventV2ContactEmployeeTypeEnumDeletedV3OldEnum `json:"old_enum,omitempty"` // 旧枚举类型
}

// EventV2ContactEmployeeTypeEnumDeletedV3OldEnum ...
type EventV2ContactEmployeeTypeEnumDeletedV3OldEnum struct {
	EnumID      string                                                       `json:"enum_id,omitempty"`      // 枚举值id
	EnumValue   string                                                       `json:"enum_value,omitempty"`   // 枚举的编号值，创建新的人员类型后，系统生成对应编号。对应[创建用户接口](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/create)中用户信息的employee_type字段值
	Content     string                                                       `json:"content,omitempty"`      // 枚举内容, 长度范围：`1` ～ `100` 字符
	EnumType    int64                                                        `json:"enum_type,omitempty"`    // 类型, 可选值有: `1`：内置类型, `2`：自定义
	EnumStatus  int64                                                        `json:"enum_status,omitempty"`  // 使用状态, 可选值有: `1`：激活, `2`：未激活
	I18nContent []*EventV2ContactEmployeeTypeEnumDeletedV3OldEnumI18nContent `json:"i18n_content,omitempty"` // i18n定义
}

// EventV2ContactEmployeeTypeEnumDeletedV3OldEnumI18nContent ...
type EventV2ContactEmployeeTypeEnumDeletedV3OldEnumI18nContent struct {
	Locale string `json:"locale,omitempty"` // 语言版本
	Value  string `json:"value,omitempty"`  // 字段名
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactEmployeeTypeEnumUpdatedV3
//
// 修改人员类型名称会发出对应事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=employee_type_enum&event=updated)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/employee_type_enum/events/updated
func (r *EventCallbackService) HandlerEventV2ContactEmployeeTypeEnumUpdatedV3(f EventV2ContactEmployeeTypeEnumUpdatedV3Handler) {
	r.cli.eventHandler.eventV2ContactEmployeeTypeEnumUpdatedV3Handler = f
}

// EventV2ContactEmployeeTypeEnumUpdatedV3Handler event EventV2ContactEmployeeTypeEnumUpdatedV3 handler
type EventV2ContactEmployeeTypeEnumUpdatedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactEmployeeTypeEnumUpdatedV3) (string, error)

// EventV2ContactEmployeeTypeEnumUpdatedV3 ...
type EventV2ContactEmployeeTypeEnumUpdatedV3 struct {
	OldEnum *EventV2ContactEmployeeTypeEnumUpdatedV3OldEnum `json:"old_enum,omitempty"` // 旧枚举类型
	NewEnum *EventV2ContactEmployeeTypeEnumUpdatedV3NewEnum `json:"new_enum,omitempty"` // 新枚举类型
}

// EventV2ContactEmployeeTypeEnumUpdatedV3OldEnum ...
type EventV2ContactEmployeeTypeEnumUpdatedV3OldEnum struct {
	EnumID      string                                                       `json:"enum_id,omitempty"`      // 枚举值id
	EnumValue   string                                                       `json:"enum_value,omitempty"`   // 枚举的编号值，创建新的人员类型后，系统生成对应编号。对应[创建用户接口](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/create)中用户信息的employee_type字段值
	Content     string                                                       `json:"content,omitempty"`      // 枚举内容, 长度范围：`1` ～ `100` 字符
	EnumType    int64                                                        `json:"enum_type,omitempty"`    // 类型, 可选值有: `1`：内置类型, `2`：自定义
	EnumStatus  int64                                                        `json:"enum_status,omitempty"`  // 使用状态, 可选值有: `1`：激活, `2`：未激活
	I18nContent []*EventV2ContactEmployeeTypeEnumUpdatedV3OldEnumI18nContent `json:"i18n_content,omitempty"` // i18n定义
}

// EventV2ContactEmployeeTypeEnumUpdatedV3OldEnumI18nContent ...
type EventV2ContactEmployeeTypeEnumUpdatedV3OldEnumI18nContent struct {
	Locale string `json:"locale,omitempty"` // 语言版本
	Value  string `json:"value,omitempty"`  // 字段名
}

// EventV2ContactEmployeeTypeEnumUpdatedV3NewEnum ...
type EventV2ContactEmployeeTypeEnumUpdatedV3NewEnum struct {
	EnumID      string                                                       `json:"enum_id,omitempty"`      // 枚举值id
	EnumValue   string                                                       `json:"enum_value,omitempty"`   // 枚举的编号值，创建新的人员类型后，系统生成对应编号。对应[创建用户接口](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/create)中用户信息的employee_type字段值
	Content     string                                                       `json:"content,omitempty"`      // 枚举内容, 长度范围：`1` ～ `100` 字符
	EnumType    int64                                                        `json:"enum_type,omitempty"`    // 类型, 可选值有: `1`：内置类型, `2`：自定义
	EnumStatus  int64                                                        `json:"enum_status,omitempty"`  // 使用状态, 可选值有: `1`：激活, `2`：未激活
	I18nContent []*EventV2ContactEmployeeTypeEnumUpdatedV3NewEnumI18nContent `json:"i18n_content,omitempty"` // i18n定义
}

// EventV2ContactEmployeeTypeEnumUpdatedV3NewEnumI18nContent ...
type EventV2ContactEmployeeTypeEnumUpdatedV3NewEnumI18nContent struct {
	Locale string `json:"locale,omitempty"` // 语言版本
	Value  string `json:"value,omitempty"`  // 字段名
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactScopeUpdatedV3
//
// 当应用通讯录范围权限发生变更时，订阅这个事件的应用会收到事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=scope&event=updated)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/scope/events/updated
func (r *EventCallbackService) HandlerEventV2ContactScopeUpdatedV3(f EventV2ContactScopeUpdatedV3Handler) {
	r.cli.eventHandler.eventV2ContactScopeUpdatedV3Handler = f
}

// EventV2ContactScopeUpdatedV3Handler event EventV2ContactScopeUpdatedV3 handler
type EventV2ContactScopeUpdatedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactScopeUpdatedV3) (string, error)

// EventV2ContactScopeUpdatedV3 ...
type EventV2ContactScopeUpdatedV3 struct {
	Added   *EventV2ContactScopeUpdatedV3Added   `json:"added,omitempty"`   // 当通讯录范围权限变更时，新增的对象
	Removed *EventV2ContactScopeUpdatedV3Removed `json:"removed,omitempty"` // 当通讯录范围权限发生变更时，移除的对象
}

// EventV2ContactScopeUpdatedV3Added ...
type EventV2ContactScopeUpdatedV3Added struct {
	Departments []*EventV2ContactScopeUpdatedV3AddedDepartment `json:"departments,omitempty"` // 部门对象
	Users       []*EventV2ContactScopeUpdatedV3AddedUser       `json:"users,omitempty"`       // 用户对象
	UserGroups  []*EventV2ContactScopeUpdatedV3AddedUserGroup  `json:"user_groups,omitempty"` // 用户组对象
}

// EventV2ContactScopeUpdatedV3AddedDepartment ...
type EventV2ContactScopeUpdatedV3AddedDepartment struct {
	Name               string                                               `json:"name,omitempty"`                 // 部门名称, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
	I18nName           *EventV2ContactScopeUpdatedV3AddedDepartmentI18nName `json:"i18n_name,omitempty"`            // 国际化的部门名称,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
	ParentDepartmentID string                                               `json:"parent_department_id,omitempty"` // 父部门的ID,* 创建根部门，该参数值为 “0”,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	DepartmentID       string                                               `json:"department_id,omitempty"`        // 本部门的自定义部门ID, 最大长度：`64` 字符, 正则校验：`^0|[^od][A-Za-z0-9]*`,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
	OpenDepartmentID   string                                               `json:"open_department_id,omitempty"`   // 部门的open_id
	LeaderUserID       string                                               `json:"leader_user_id,omitempty"`       // 部门主管用户ID,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	ChatID             string                                               `json:"chat_id,omitempty"`              // 部门群ID,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
	Order              string                                               `json:"order,omitempty"`                // 部门的排序，即部门在其同级部门的展示顺序,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	UnitIDs            []string                                             `json:"unit_ids,omitempty"`             // 部门单位自定义ID列表，当前只支持一个,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	MemberCount        int64                                                `json:"member_count,omitempty"`         // 部门下用户的个数,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	Status             *EventV2ContactScopeUpdatedV3AddedDepartmentStatus   `json:"status,omitempty"`               // 部门状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
}

// EventV2ContactScopeUpdatedV3AddedDepartmentI18nName ...
type EventV2ContactScopeUpdatedV3AddedDepartmentI18nName struct {
	ZhCn string `json:"zh_cn,omitempty"` // 部门的中文名
	JaJp string `json:"ja_jp,omitempty"` // 部门的日文名
	EnUs string `json:"en_us,omitempty"` // 部门的英文名
}

// EventV2ContactScopeUpdatedV3AddedDepartmentStatus ...
type EventV2ContactScopeUpdatedV3AddedDepartmentStatus struct {
	IsDeleted bool `json:"is_deleted,omitempty"` // 是否被删除
}

// EventV2ContactScopeUpdatedV3AddedUser ...
type EventV2ContactScopeUpdatedV3AddedUser struct {
	UnionID              string                                                   `json:"union_id,omitempty"`               // 用户的union_id，不同ID的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UserID               string                                                   `json:"user_id,omitempty"`                // 租户内用户的唯一标识，用户的user_id，不同ID的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction), 字段权限要求: 获取用户 user ID
	OpenID               string                                                   `json:"open_id,omitempty"`                // 用户的open_id，不同ID的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Name                 string                                                   `json:"name,omitempty"`                   // 用户名, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	EnName               string                                                   `json:"en_name,omitempty"`                // 英文名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Nickname             string                                                   `json:"nickname,omitempty"`               // 别名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Email                string                                                   `json:"email,omitempty"`                  // 邮箱, 字段权限要求: 获取用户邮箱信息
	Mobile               string                                                   `json:"mobile,omitempty"`                 // 手机号，中国大陆手机可不填区号，境外手机需加国际电话区号前缀。, 字段权限要求: 获取用户手机号
	Gender               int64                                                    `json:"gender,omitempty"`                 // 性别, 可选值有: `0`：保密, `1`：男, `2`：女,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户性别,以应用身份访问通讯录,读取通讯录
	Avatar               *EventV2ContactScopeUpdatedV3AddedUserAvatar             `json:"avatar,omitempty"`                 // 用户头像信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Status               *EventV2ContactScopeUpdatedV3AddedUserStatus             `json:"status,omitempty"`                 // 用户状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	LeaderUserID         string                                                   `json:"leader_user_id,omitempty"`         // 用户的直接主管的用户ID，ID值与查询参数中的user_id_type 对应。,不同 ID 的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	City                 string                                                   `json:"city,omitempty"`                   // 城市,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Country              string                                                   `json:"country,omitempty"`                // 国家或地区Code缩写，具体写入格式请参考 [国家/地区码表](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/country-code-description),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	WorkStation          string                                                   `json:"work_station,omitempty"`           // 工位,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	JoinTime             int64                                                    `json:"join_time,omitempty"`              // 入职时间,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeNo           string                                                   `json:"employee_no,omitempty"`            // 工号,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeType         int64                                                    `json:"employee_type,omitempty"`          // 员工类型，可选值有：, `1`：正式员工, `2`：实习生, `3`：外包, `4`：劳务, `5`：顾问   ,同时可读取到自定义员工类型的 int 值，可通过下方接口获取到该租户的自定义员工类型的名称   ,[获取人员类型](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/employee_type_enum/list),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	CustomAttrs          []*EventV2ContactScopeUpdatedV3AddedUserCustomAttr       `json:"custom_attrs,omitempty"`           // 自定义字段，请确保你的组织管理员已在管理后台/组织架构/成员字段管理/自定义字段管理/全局设置中开启了“允许开放平台 API 调用“，否则该字段不会生效/返回。,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EnterpriseEmail      string                                                   `json:"enterprise_email,omitempty"`       // 企业邮箱，请先确保已在管理后台启用飞书邮箱服务,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	JobTitle             string                                                   `json:"job_title,omitempty"`              // 职务,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	NeedSendNotification bool                                                     `json:"need_send_notification,omitempty"` // 是否发送提示消息
	NotificationOption   *EventV2ContactScopeUpdatedV3AddedUserNotificationOption `json:"notification_option,omitempty"`    // 创建用户的邀请方式
	IsFrozen             bool                                                     `json:"is_frozen,omitempty"`              // 是否暂停用户
}

// EventV2ContactScopeUpdatedV3AddedUserAvatar ...
type EventV2ContactScopeUpdatedV3AddedUserAvatar struct {
	Avatar72     string `json:"avatar_72,omitempty"`     // 72*72像素头像链接
	Avatar240    string `json:"avatar_240,omitempty"`    // 240*240像素头像链接
	Avatar640    string `json:"avatar_640,omitempty"`    // 640*640像素头像链接
	AvatarOrigin string `json:"avatar_origin,omitempty"` // 原始头像链接
}

// EventV2ContactScopeUpdatedV3AddedUserStatus ...
type EventV2ContactScopeUpdatedV3AddedUserStatus struct {
	IsFrozen    bool `json:"is_frozen,omitempty"`    // 是否暂停
	IsResigned  bool `json:"is_resigned,omitempty"`  // 是否离职
	IsActivated bool `json:"is_activated,omitempty"` // 是否激活
	IsExited    bool `json:"is_exited,omitempty"`    // 是否主动退出，主动退出一段时间后用户会自动转为已离职
	IsUnjoin    bool `json:"is_unjoin,omitempty"`    // 是否未加入，需要用户自主确认才能加入团队
}

// EventV2ContactScopeUpdatedV3AddedUserCustomAttr ...
type EventV2ContactScopeUpdatedV3AddedUserCustomAttr struct {
	Type  string                                                `json:"type,omitempty"`  // 自定义字段类型   , `TEXT`：文本, `HREF`：网页, `ENUMERATION`：枚举, `PICTURE_ENUM`：图片, `GENERIC_USER`：用户,[自定义字段相关常见问题](https://open.feishu.cn/document/ugTN1YjL4UTN24CO1UjN/uQzN1YjL0cTN24CN3UjN)
	ID    string                                                `json:"id,omitempty"`    // 自定义字段ID
	Value *EventV2ContactScopeUpdatedV3AddedUserCustomAttrValue `json:"value,omitempty"` // 自定义字段取值
}

// EventV2ContactScopeUpdatedV3AddedUserCustomAttrValue ...
type EventV2ContactScopeUpdatedV3AddedUserCustomAttrValue struct {
	Text        string                                                           `json:"text,omitempty"`         // 字段类型为`TEXT`时该参数定义字段值，必填；字段类型为`HREF`时该参数定义网页标题，必填
	URL         string                                                           `json:"url,omitempty"`          // 字段类型为 HREF 时，该参数定义默认 URL
	PcURL       string                                                           `json:"pc_url,omitempty"`       // 字段类型为 HREF 时，该参数定义PC端 URL
	OptionID    string                                                           `json:"option_id,omitempty"`    // 字段类型为 ENUMERATION 或 PICTURE_ENUM 时，该参数定义选项值
	OptionValue string                                                           `json:"option_value,omitempty"` // 选项值
	Name        string                                                           `json:"name,omitempty"`         // 名称
	PictureURL  string                                                           `json:"picture_url,omitempty"`  // 图片链接
	GenericUser *EventV2ContactScopeUpdatedV3AddedUserCustomAttrValueGenericUser `json:"generic_user,omitempty"` // 字段类型为 GENERIC_USER 时，该参数定义引用人员
}

// EventV2ContactScopeUpdatedV3AddedUserCustomAttrValueGenericUser ...
type EventV2ContactScopeUpdatedV3AddedUserCustomAttrValueGenericUser struct {
	ID   string `json:"id,omitempty"`   // 用户的user_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Type int64  `json:"type,omitempty"` // 用户类型    1：用户
}

// EventV2ContactScopeUpdatedV3AddedUserNotificationOption ...
type EventV2ContactScopeUpdatedV3AddedUserNotificationOption struct {
	Channels []string `json:"channels,omitempty"` // 通道列表，枚举值，可多选：, `sms`：短信邀请, `email`：邮件邀请
	Language string   `json:"language,omitempty"` // 语言类型, 可选值有: `zh-CN`：中文, `en-US`：英文, `ja-JP`：日文
}

// EventV2ContactScopeUpdatedV3AddedUserGroup ...
type EventV2ContactScopeUpdatedV3AddedUserGroup struct {
	UserGroupID string `json:"user_group_id,omitempty"` // 用户组的自定义ID, 长度范围：`1` ～ `64` 字符
	Name        string `json:"name,omitempty"`          // 用户组的名称, 长度范围：`1` ～ `100` 字符
	Type        int64  `json:"type,omitempty"`          // 用户组的类型, 可选值有: `1`：普通用户组, `2`：动态用户组
	MemberCount int64  `json:"member_count,omitempty"`  // 成员数量
	Status      int64  `json:"status,omitempty"`        // 用户组状态, 可选值有: `0`：未知, `1`：计算完毕, `2`：计算中, `3`：计算失败
}

// EventV2ContactScopeUpdatedV3Removed ...
type EventV2ContactScopeUpdatedV3Removed struct {
	Departments []*EventV2ContactScopeUpdatedV3RemovedDepartment `json:"departments,omitempty"` // 部门对象
	Users       []*EventV2ContactScopeUpdatedV3RemovedUser       `json:"users,omitempty"`       // 用户对象
	UserGroups  []*EventV2ContactScopeUpdatedV3RemovedUserGroup  `json:"user_groups,omitempty"` // 用户组对象
}

// EventV2ContactScopeUpdatedV3RemovedDepartment ...
type EventV2ContactScopeUpdatedV3RemovedDepartment struct {
	Name               string                                                 `json:"name,omitempty"`                 // 部门名称, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
	I18nName           *EventV2ContactScopeUpdatedV3RemovedDepartmentI18nName `json:"i18n_name,omitempty"`            // 国际化的部门名称,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
	ParentDepartmentID string                                                 `json:"parent_department_id,omitempty"` // 父部门的ID,* 创建根部门，该参数值为 “0”,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	DepartmentID       string                                                 `json:"department_id,omitempty"`        // 本部门的自定义部门ID, 最大长度：`64` 字符, 正则校验：`^0|[^od][A-Za-z0-9]*`,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
	OpenDepartmentID   string                                                 `json:"open_department_id,omitempty"`   // 部门的open_id
	LeaderUserID       string                                                 `json:"leader_user_id,omitempty"`       // 部门主管用户ID,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	ChatID             string                                                 `json:"chat_id,omitempty"`              // 部门群ID,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
	Order              string                                                 `json:"order,omitempty"`                // 部门的排序，即部门在其同级部门的展示顺序,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	UnitIDs            []string                                               `json:"unit_ids,omitempty"`             // 部门单位自定义ID列表，当前只支持一个,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	MemberCount        int64                                                  `json:"member_count,omitempty"`         // 部门下用户的个数,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门组织架构信息,以应用身份访问通讯录,读取通讯录
	Status             *EventV2ContactScopeUpdatedV3RemovedDepartmentStatus   `json:"status,omitempty"`               // 部门状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取部门基础信息,以应用身份访问通讯录,读取通讯录
}

// EventV2ContactScopeUpdatedV3RemovedDepartmentI18nName ...
type EventV2ContactScopeUpdatedV3RemovedDepartmentI18nName struct {
	ZhCn string `json:"zh_cn,omitempty"` // 部门的中文名
	JaJp string `json:"ja_jp,omitempty"` // 部门的日文名
	EnUs string `json:"en_us,omitempty"` // 部门的英文名
}

// EventV2ContactScopeUpdatedV3RemovedDepartmentStatus ...
type EventV2ContactScopeUpdatedV3RemovedDepartmentStatus struct {
	IsDeleted bool `json:"is_deleted,omitempty"` // 是否被删除
}

// EventV2ContactScopeUpdatedV3RemovedUser ...
type EventV2ContactScopeUpdatedV3RemovedUser struct {
	UnionID              string                                                     `json:"union_id,omitempty"`               // 用户的union_id，不同ID的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UserID               string                                                     `json:"user_id,omitempty"`                // 租户内用户的唯一标识，用户的user_id，不同ID的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction), 字段权限要求: 获取用户 user ID
	OpenID               string                                                     `json:"open_id,omitempty"`                // 用户的open_id，不同ID的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Name                 string                                                     `json:"name,omitempty"`                   // 用户名, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	EnName               string                                                     `json:"en_name,omitempty"`                // 英文名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Nickname             string                                                     `json:"nickname,omitempty"`               // 别名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Email                string                                                     `json:"email,omitempty"`                  // 邮箱, 字段权限要求: 获取用户邮箱信息
	Mobile               string                                                     `json:"mobile,omitempty"`                 // 手机号，中国大陆手机可不填区号，境外手机需加国际电话区号前缀。, 字段权限要求: 获取用户手机号
	Gender               int64                                                      `json:"gender,omitempty"`                 // 性别, 可选值有: `0`：保密, `1`：男, `2`：女,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户性别,以应用身份访问通讯录,读取通讯录
	Avatar               *EventV2ContactScopeUpdatedV3RemovedUserAvatar             `json:"avatar,omitempty"`                 // 用户头像信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Status               *EventV2ContactScopeUpdatedV3RemovedUserStatus             `json:"status,omitempty"`                 // 用户状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	LeaderUserID         string                                                     `json:"leader_user_id,omitempty"`         // 用户的直接主管的用户ID，ID值与查询参数中的user_id_type 对应。,不同 ID 的说明参见 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	City                 string                                                     `json:"city,omitempty"`                   // 城市,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Country              string                                                     `json:"country,omitempty"`                // 国家或地区Code缩写，具体写入格式请参考 [国家/地区码表](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/country-code-description),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	WorkStation          string                                                     `json:"work_station,omitempty"`           // 工位,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	JoinTime             int64                                                      `json:"join_time,omitempty"`              // 入职时间,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeNo           string                                                     `json:"employee_no,omitempty"`            // 工号,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeType         int64                                                      `json:"employee_type,omitempty"`          // 员工类型，可选值有：, `1`：正式员工, `2`：实习生, `3`：外包, `4`：劳务, `5`：顾问   ,同时可读取到自定义员工类型的 int 值，可通过下方接口获取到该租户的自定义员工类型的名称   ,[获取人员类型](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/employee_type_enum/list),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	CustomAttrs          []*EventV2ContactScopeUpdatedV3RemovedUserCustomAttr       `json:"custom_attrs,omitempty"`           // 自定义字段，请确保你的组织管理员已在管理后台/组织架构/成员字段管理/自定义字段管理/全局设置中开启了“允许开放平台 API 调用“，否则该字段不会生效/返回。,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EnterpriseEmail      string                                                     `json:"enterprise_email,omitempty"`       // 企业邮箱，请先确保已在管理后台启用飞书邮箱服务,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	JobTitle             string                                                     `json:"job_title,omitempty"`              // 职务,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	NeedSendNotification bool                                                       `json:"need_send_notification,omitempty"` // 是否发送提示消息
	NotificationOption   *EventV2ContactScopeUpdatedV3RemovedUserNotificationOption `json:"notification_option,omitempty"`    // 创建用户的邀请方式
	IsFrozen             bool                                                       `json:"is_frozen,omitempty"`              // 是否暂停用户
}

// EventV2ContactScopeUpdatedV3RemovedUserAvatar ...
type EventV2ContactScopeUpdatedV3RemovedUserAvatar struct {
	Avatar72     string `json:"avatar_72,omitempty"`     // 72*72像素头像链接
	Avatar240    string `json:"avatar_240,omitempty"`    // 240*240像素头像链接
	Avatar640    string `json:"avatar_640,omitempty"`    // 640*640像素头像链接
	AvatarOrigin string `json:"avatar_origin,omitempty"` // 原始头像链接
}

// EventV2ContactScopeUpdatedV3RemovedUserStatus ...
type EventV2ContactScopeUpdatedV3RemovedUserStatus struct {
	IsFrozen    bool `json:"is_frozen,omitempty"`    // 是否暂停
	IsResigned  bool `json:"is_resigned,omitempty"`  // 是否离职
	IsActivated bool `json:"is_activated,omitempty"` // 是否激活
	IsExited    bool `json:"is_exited,omitempty"`    // 是否主动退出，主动退出一段时间后用户会自动转为已离职
	IsUnjoin    bool `json:"is_unjoin,omitempty"`    // 是否未加入，需要用户自主确认才能加入团队
}

// EventV2ContactScopeUpdatedV3RemovedUserCustomAttr ...
type EventV2ContactScopeUpdatedV3RemovedUserCustomAttr struct {
	Type  string                                                  `json:"type,omitempty"`  // 自定义字段类型   , `TEXT`：文本, `HREF`：网页, `ENUMERATION`：枚举, `PICTURE_ENUM`：图片, `GENERIC_USER`：用户,[自定义字段相关常见问题](https://open.feishu.cn/document/ugTN1YjL4UTN24CO1UjN/uQzN1YjL0cTN24CN3UjN)
	ID    string                                                  `json:"id,omitempty"`    // 自定义字段ID
	Value *EventV2ContactScopeUpdatedV3RemovedUserCustomAttrValue `json:"value,omitempty"` // 自定义字段取值
}

// EventV2ContactScopeUpdatedV3RemovedUserCustomAttrValue ...
type EventV2ContactScopeUpdatedV3RemovedUserCustomAttrValue struct {
	Text        string                                                             `json:"text,omitempty"`         // 字段类型为`TEXT`时该参数定义字段值，必填；字段类型为`HREF`时该参数定义网页标题，必填
	URL         string                                                             `json:"url,omitempty"`          // 字段类型为 HREF 时，该参数定义默认 URL
	PcURL       string                                                             `json:"pc_url,omitempty"`       // 字段类型为 HREF 时，该参数定义PC端 URL
	OptionID    string                                                             `json:"option_id,omitempty"`    // 字段类型为 ENUMERATION 或 PICTURE_ENUM 时，该参数定义选项值
	OptionValue string                                                             `json:"option_value,omitempty"` // 选项值
	Name        string                                                             `json:"name,omitempty"`         // 名称
	PictureURL  string                                                             `json:"picture_url,omitempty"`  // 图片链接
	GenericUser *EventV2ContactScopeUpdatedV3RemovedUserCustomAttrValueGenericUser `json:"generic_user,omitempty"` // 字段类型为 GENERIC_USER 时，该参数定义引用人员
}

// EventV2ContactScopeUpdatedV3RemovedUserCustomAttrValueGenericUser ...
type EventV2ContactScopeUpdatedV3RemovedUserCustomAttrValueGenericUser struct {
	ID   string `json:"id,omitempty"`   // 用户的user_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Type int64  `json:"type,omitempty"` // 用户类型    1：用户
}

// EventV2ContactScopeUpdatedV3RemovedUserNotificationOption ...
type EventV2ContactScopeUpdatedV3RemovedUserNotificationOption struct {
	Channels []string `json:"channels,omitempty"` // 通道列表，枚举值，可多选：, `sms`：短信邀请, `email`：邮件邀请
	Language string   `json:"language,omitempty"` // 语言类型, 可选值有: `zh-CN`：中文, `en-US`：英文, `ja-JP`：日文
}

// EventV2ContactScopeUpdatedV3RemovedUserGroup ...
type EventV2ContactScopeUpdatedV3RemovedUserGroup struct {
	UserGroupID string `json:"user_group_id,omitempty"` // 用户组的自定义ID, 长度范围：`1` ～ `64` 字符
	Name        string `json:"name,omitempty"`          // 用户组的名称, 长度范围：`1` ～ `100` 字符
	Type        int64  `json:"type,omitempty"`          // 用户组的类型, 可选值有: `1`：普通用户组, `2`：动态用户组
	MemberCount int64  `json:"member_count,omitempty"`  // 成员数量
	Status      int64  `json:"status,omitempty"`        // 用户组状态, 可选值有: `0`：未知, `1`：计算完毕, `2`：计算中, `3`：计算失败
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactUserCreatedV3
//
// 通过该事件订阅员工入职。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=user&event=created)
// 只有当应用拥有被改动字段的数据权限时，才会接收到事件。具体的数据权限与字段的关系请参考[应用权限](https://open.feishu.cn/document/ukTMukTMukTM/uQjN3QjL0YzN04CN2cDN)，或查看事件体参数列表的字段描述。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/events/created
func (r *EventCallbackService) HandlerEventV2ContactUserCreatedV3(f EventV2ContactUserCreatedV3Handler) {
	r.cli.eventHandler.eventV2ContactUserCreatedV3Handler = f
}

// EventV2ContactUserCreatedV3Handler event EventV2ContactUserCreatedV3 handler
type EventV2ContactUserCreatedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactUserCreatedV3) (string, error)

// EventV2ContactUserCreatedV3 ...
type EventV2ContactUserCreatedV3 struct {
	Object *EventV2ContactUserCreatedV3Object `json:"object,omitempty"` // 事件信息
}

// EventV2ContactUserCreatedV3Object ...
type EventV2ContactUserCreatedV3Object struct {
	OpenID          string                                         `json:"open_id,omitempty"`          // 用户的open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UnionID         string                                         `json:"union_id,omitempty"`         // 用户的union_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UserID          string                                         `json:"user_id,omitempty"`          // 租户内用户的唯一标识 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction), 字段权限要求: 获取用户 user ID
	Name            string                                         `json:"name,omitempty"`             // 用户名, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	EnName          string                                         `json:"en_name,omitempty"`          // 英文名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Nickname        string                                         `json:"nickname,omitempty"`         // 别名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Email           string                                         `json:"email,omitempty"`            // 邮箱, 字段权限要求: 获取用户邮箱信息
	EnterpriseEmail string                                         `json:"enterprise_email,omitempty"` // 企业邮箱
	JobTitle        string                                         `json:"job_title,omitempty"`        // 职务
	Mobile          string                                         `json:"mobile,omitempty"`           // 手机号, 字段权限要求: 获取用户手机号
	Gender          int64                                          `json:"gender,omitempty"`           // 性别, 可选值有: `0`：未知, `1`：男, `2`：女,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户性别,以应用身份访问通讯录,读取通讯录
	Avatar          *EventV2ContactUserCreatedV3ObjectAvatar       `json:"avatar,omitempty"`           // 用户头像信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Status          *EventV2ContactUserCreatedV3ObjectStatus       `json:"status,omitempty"`           // 用户状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	DepartmentIDs   []string                                       `json:"department_ids,omitempty"`   // 用户所属部门的ID列表,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	LeaderUserID    string                                         `json:"leader_user_id,omitempty"`   // 用户的直接主管的用户open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	City            string                                         `json:"city,omitempty"`             // 城市,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Country         string                                         `json:"country,omitempty"`          // 国家,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	WorkStation     string                                         `json:"work_station,omitempty"`     // 工位,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	JoinTime        int64                                          `json:"join_time,omitempty"`        // 入职时间, 取值范围：`1` ～ `2147483647`,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeNo      string                                         `json:"employee_no,omitempty"`      // 工号,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeType    int64                                          `json:"employee_type,omitempty"`    // 员工类型, 可选值有: `1`：正式员工, `2`：实习生, `3`：外包, `4`：劳务, `5`：顾问,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Orders          []*EventV2ContactUserCreatedV3ObjectOrder      `json:"orders,omitempty"`           // 用户排序信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	CustomAttrs     []*EventV2ContactUserCreatedV3ObjectCustomAttr `json:"custom_attrs,omitempty"`     // 自定义属性,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
}

// EventV2ContactUserCreatedV3ObjectAvatar ...
type EventV2ContactUserCreatedV3ObjectAvatar struct {
	Avatar72     string `json:"avatar_72,omitempty"`     // 72*72像素头像链接
	Avatar240    string `json:"avatar_240,omitempty"`    // 240*240像素头像链接
	Avatar640    string `json:"avatar_640,omitempty"`    // 640*640像素头像链接
	AvatarOrigin string `json:"avatar_origin,omitempty"` // 原始头像链接
}

// EventV2ContactUserCreatedV3ObjectStatus ...
type EventV2ContactUserCreatedV3ObjectStatus struct {
	IsFrozen    bool `json:"is_frozen,omitempty"`    // 是否暂停
	IsResigned  bool `json:"is_resigned,omitempty"`  // 是否离职
	IsActivated bool `json:"is_activated,omitempty"` // 是否激活
	IsExited    bool `json:"is_exited,omitempty"`    // 是否主动退出，主动退出一段时间后用户会自动转为已离职
	IsUnjoin    bool `json:"is_unjoin,omitempty"`    // 是否未加入，需要用户自主确认才能加入团队
}

// EventV2ContactUserCreatedV3ObjectOrder ...
type EventV2ContactUserCreatedV3ObjectOrder struct {
	DepartmentID    string `json:"department_id,omitempty"`    // 排序信息对应的部门ID, ID值与查询参数中的department_id_type 对应。,不同 ID 的说明参见 [部门ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview)
	UserOrder       int64  `json:"user_order,omitempty"`       // 用户在其直属部门内的排序，数值越大，排序越靠前
	DepartmentOrder int64  `json:"department_order,omitempty"` // 用户所属的多个部门间的排序，数值越大，排序越靠前
}

// EventV2ContactUserCreatedV3ObjectCustomAttr ...
type EventV2ContactUserCreatedV3ObjectCustomAttr struct {
	Type  string                                            `json:"type,omitempty"`  // 自定义字段类型   , `TEXT`：文本, `HREF`：网页, `ENUMERATION`：枚举, `PICTURE_ENUM`：图片, `GENERIC_USER`：用户,[自定义字段相关常见问题](https://open.feishu.cn/document/ugTN1YjL4UTN24CO1UjN/uQzN1YjL0cTN24CN3UjN)
	ID    string                                            `json:"id,omitempty"`    // 自定义字段ID
	Value *EventV2ContactUserCreatedV3ObjectCustomAttrValue `json:"value,omitempty"` // 自定义字段取值
}

// EventV2ContactUserCreatedV3ObjectCustomAttrValue ...
type EventV2ContactUserCreatedV3ObjectCustomAttrValue struct {
	Text        string                                                       `json:"text,omitempty"`         // 字段类型为`TEXT`时该参数定义字段值，必填；字段类型为`HREF`时该参数定义网页标题，必填
	URL         string                                                       `json:"url,omitempty"`          // 字段类型为 HREF 时，该参数定义默认 URL
	PcURL       string                                                       `json:"pc_url,omitempty"`       // 字段类型为 HREF 时，该参数定义PC端 URL
	OptionID    string                                                       `json:"option_id,omitempty"`    // 字段类型为 ENUMERATION 或 PICTURE_ENUM 时，该参数定义选项值
	OptionValue string                                                       `json:"option_value,omitempty"` // 选项值
	Name        string                                                       `json:"name,omitempty"`         // 名称
	PictureURL  string                                                       `json:"picture_url,omitempty"`  // 图片链接
	GenericUser *EventV2ContactUserCreatedV3ObjectCustomAttrValueGenericUser `json:"generic_user,omitempty"` // 字段类型为 GENERIC_USER 时，该参数定义引用人员
}

// EventV2ContactUserCreatedV3ObjectCustomAttrValueGenericUser ...
type EventV2ContactUserCreatedV3ObjectCustomAttrValueGenericUser struct {
	ID   string `json:"id,omitempty"`   // 用户的user_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Type int64  `json:"type,omitempty"` // 用户类型    1：用户
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactUserDeletedV3
//
// 通过该事件订阅员工离职。应用身份访问通讯录的权限为历史版本，不推荐申请。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=user&event=deleted)
// 只有当应用拥有被改动字段的数据权限时，才会接收到事件。具体的数据权限与字段的关系请参考[应用权限](https://open.feishu.cn/document/ukTMukTMukTM/uQjN3QjL0YzN04CN2cDN)，或查看事件体参数列表的字段描述。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/events/deleted
func (r *EventCallbackService) HandlerEventV2ContactUserDeletedV3(f EventV2ContactUserDeletedV3Handler) {
	r.cli.eventHandler.eventV2ContactUserDeletedV3Handler = f
}

// EventV2ContactUserDeletedV3Handler event EventV2ContactUserDeletedV3 handler
type EventV2ContactUserDeletedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactUserDeletedV3) (string, error)

// EventV2ContactUserDeletedV3 ...
type EventV2ContactUserDeletedV3 struct {
	Object    *EventV2ContactUserDeletedV3Object    `json:"object,omitempty"`     // 员工信息
	OldObject *EventV2ContactUserDeletedV3OldObject `json:"old_object,omitempty"` // 删除前信息
}

// EventV2ContactUserDeletedV3Object ...
type EventV2ContactUserDeletedV3Object struct {
	OpenID          string                                         `json:"open_id,omitempty"`          // 用户的open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UnionID         string                                         `json:"union_id,omitempty"`         // 用户的union_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UserID          string                                         `json:"user_id,omitempty"`          // 租户内用户的唯一标识 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction), 字段权限要求: 获取用户 user ID
	Name            string                                         `json:"name,omitempty"`             // 用户名, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	EnName          string                                         `json:"en_name,omitempty"`          // 英文名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Nickname        string                                         `json:"nickname,omitempty"`         // 别名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Email           string                                         `json:"email,omitempty"`            // 邮箱, 字段权限要求: 获取用户邮箱信息
	EnterpriseEmail string                                         `json:"enterprise_email,omitempty"` // 企业邮箱
	JobTitle        string                                         `json:"job_title,omitempty"`        // 职务
	Mobile          string                                         `json:"mobile,omitempty"`           // 手机号, 字段权限要求: 获取用户手机号
	Gender          int64                                          `json:"gender,omitempty"`           // 性别, 可选值有: `0`：未知, `1`：男, `2`：女,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户性别,以应用身份访问通讯录,读取通讯录
	Avatar          *EventV2ContactUserDeletedV3ObjectAvatar       `json:"avatar,omitempty"`           // 用户头像信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Status          *EventV2ContactUserDeletedV3ObjectStatus       `json:"status,omitempty"`           // 用户状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	DepartmentIDs   []string                                       `json:"department_ids,omitempty"`   // 用户所属部门的ID列表,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	LeaderUserID    string                                         `json:"leader_user_id,omitempty"`   // 用户的直接主管的用户open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	City            string                                         `json:"city,omitempty"`             // 城市,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Country         string                                         `json:"country,omitempty"`          // 国家,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	WorkStation     string                                         `json:"work_station,omitempty"`     // 工位,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	JoinTime        int64                                          `json:"join_time,omitempty"`        // 入职时间, 取值范围：`1` ～ `2147483647`,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeNo      string                                         `json:"employee_no,omitempty"`      // 工号,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeType    int64                                          `json:"employee_type,omitempty"`    // 员工类型, 可选值有: `1`：正式员工, `2`：实习生, `3`：外包, `4`：劳务, `5`：顾问,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Orders          []*EventV2ContactUserDeletedV3ObjectOrder      `json:"orders,omitempty"`           // 用户排序信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	CustomAttrs     []*EventV2ContactUserDeletedV3ObjectCustomAttr `json:"custom_attrs,omitempty"`     // 自定义属性,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
}

// EventV2ContactUserDeletedV3ObjectAvatar ...
type EventV2ContactUserDeletedV3ObjectAvatar struct {
	Avatar72     string `json:"avatar_72,omitempty"`     // 72*72像素头像链接
	Avatar240    string `json:"avatar_240,omitempty"`    // 240*240像素头像链接
	Avatar640    string `json:"avatar_640,omitempty"`    // 640*640像素头像链接
	AvatarOrigin string `json:"avatar_origin,omitempty"` // 原始头像链接
}

// EventV2ContactUserDeletedV3ObjectStatus ...
type EventV2ContactUserDeletedV3ObjectStatus struct {
	IsFrozen    bool `json:"is_frozen,omitempty"`    // 是否暂停
	IsResigned  bool `json:"is_resigned,omitempty"`  // 是否离职
	IsActivated bool `json:"is_activated,omitempty"` // 是否激活
	IsExited    bool `json:"is_exited,omitempty"`    // 是否主动退出，主动退出一段时间后用户会自动转为已离职
	IsUnjoin    bool `json:"is_unjoin,omitempty"`    // 是否未加入，需要用户自主确认才能加入团队
}

// EventV2ContactUserDeletedV3ObjectOrder ...
type EventV2ContactUserDeletedV3ObjectOrder struct {
	DepartmentID    string `json:"department_id,omitempty"`    // 排序信息对应的部门ID, ID值与查询参数中的department_id_type 对应。,不同 ID 的说明参见 [部门ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview)
	UserOrder       int64  `json:"user_order,omitempty"`       // 用户在其直属部门内的排序，数值越大，排序越靠前
	DepartmentOrder int64  `json:"department_order,omitempty"` // 用户所属的多个部门间的排序，数值越大，排序越靠前
}

// EventV2ContactUserDeletedV3ObjectCustomAttr ...
type EventV2ContactUserDeletedV3ObjectCustomAttr struct {
	Type  string                                            `json:"type,omitempty"`  // 自定义字段类型   , `TEXT`：文本, `HREF`：网页, `ENUMERATION`：枚举, `PICTURE_ENUM`：图片, `GENERIC_USER`：用户,[自定义字段相关常见问题](https://open.feishu.cn/document/ugTN1YjL4UTN24CO1UjN/uQzN1YjL0cTN24CN3UjN)
	ID    string                                            `json:"id,omitempty"`    // 自定义字段ID
	Value *EventV2ContactUserDeletedV3ObjectCustomAttrValue `json:"value,omitempty"` // 自定义字段取值
}

// EventV2ContactUserDeletedV3ObjectCustomAttrValue ...
type EventV2ContactUserDeletedV3ObjectCustomAttrValue struct {
	Text        string                                                       `json:"text,omitempty"`         // 字段类型为`TEXT`时该参数定义字段值，必填；字段类型为`HREF`时该参数定义网页标题，必填
	URL         string                                                       `json:"url,omitempty"`          // 字段类型为 HREF 时，该参数定义默认 URL
	PcURL       string                                                       `json:"pc_url,omitempty"`       // 字段类型为 HREF 时，该参数定义PC端 URL
	OptionID    string                                                       `json:"option_id,omitempty"`    // 字段类型为 ENUMERATION 或 PICTURE_ENUM 时，该参数定义选项值
	OptionValue string                                                       `json:"option_value,omitempty"` // 选项值
	Name        string                                                       `json:"name,omitempty"`         // 名称
	PictureURL  string                                                       `json:"picture_url,omitempty"`  // 图片链接
	GenericUser *EventV2ContactUserDeletedV3ObjectCustomAttrValueGenericUser `json:"generic_user,omitempty"` // 字段类型为 GENERIC_USER 时，该参数定义引用人员
}

// EventV2ContactUserDeletedV3ObjectCustomAttrValueGenericUser ...
type EventV2ContactUserDeletedV3ObjectCustomAttrValueGenericUser struct {
	ID   string `json:"id,omitempty"`   // 用户的user_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Type int64  `json:"type,omitempty"` // 用户类型    1：用户
}

// EventV2ContactUserDeletedV3OldObject ...
type EventV2ContactUserDeletedV3OldObject struct {
	DepartmentIDs []string `json:"department_ids,omitempty"` // 用户所属部门的ID列表,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	OpenID        string   `json:"open_id,omitempty"`        // 用户open_id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2ContactUserUpdatedV3
//
// 通过该事件订阅员工变更。old_object中只展示更新的字段的原始值。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=contact&version=v3&resource=user&event=updated)
// 只有当应用拥有被改动字段的数据权限时，才会接收到事件。具体的数据权限与字段的关系请参考[应用权限](https://open.feishu.cn/document/ukTMukTMukTM/uQjN3QjL0YzN04CN2cDN)，或查看事件体参数列表的字段描述。
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/user/events/updated
func (r *EventCallbackService) HandlerEventV2ContactUserUpdatedV3(f EventV2ContactUserUpdatedV3Handler) {
	r.cli.eventHandler.eventV2ContactUserUpdatedV3Handler = f
}

// EventV2ContactUserUpdatedV3Handler event EventV2ContactUserUpdatedV3 handler
type EventV2ContactUserUpdatedV3Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2ContactUserUpdatedV3) (string, error)

// EventV2ContactUserUpdatedV3 ...
type EventV2ContactUserUpdatedV3 struct {
	Object    *EventV2ContactUserUpdatedV3Object    `json:"object,omitempty"`     // 变更后信息
	OldObject *EventV2ContactUserUpdatedV3OldObject `json:"old_object,omitempty"` // 变更前信息，body中只包含有变更的字段
}

// EventV2ContactUserUpdatedV3Object ...
type EventV2ContactUserUpdatedV3Object struct {
	OpenID          string                                         `json:"open_id,omitempty"`          // 用户的open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UnionID         string                                         `json:"union_id,omitempty"`         // 用户的union_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UserID          string                                         `json:"user_id,omitempty"`          // 租户内用户的唯一标识 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction), 字段权限要求: 获取用户 user ID
	Name            string                                         `json:"name,omitempty"`             // 用户名, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	EnName          string                                         `json:"en_name,omitempty"`          // 英文名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Nickname        string                                         `json:"nickname,omitempty"`         // 别名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Email           string                                         `json:"email,omitempty"`            // 邮箱, 字段权限要求: 获取用户邮箱信息
	EnterpriseEmail string                                         `json:"enterprise_email,omitempty"` // 企业邮箱
	JobTitle        string                                         `json:"job_title,omitempty"`        // 职务
	Mobile          string                                         `json:"mobile,omitempty"`           // 手机号, 字段权限要求: 获取用户手机号
	Gender          int64                                          `json:"gender,omitempty"`           // 性别, 可选值有: `0`：未知, `1`：男, `2`：女,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户性别,以应用身份访问通讯录,读取通讯录
	Avatar          *EventV2ContactUserUpdatedV3ObjectAvatar       `json:"avatar,omitempty"`           // 用户头像信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Status          *EventV2ContactUserUpdatedV3ObjectStatus       `json:"status,omitempty"`           // 用户状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	DepartmentIDs   []string                                       `json:"department_ids,omitempty"`   // 用户所属部门的ID列表,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	LeaderUserID    string                                         `json:"leader_user_id,omitempty"`   // 用户的直接主管的用户open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	City            string                                         `json:"city,omitempty"`             // 城市,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Country         string                                         `json:"country,omitempty"`          // 国家,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	WorkStation     string                                         `json:"work_station,omitempty"`     // 工位,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	JoinTime        int64                                          `json:"join_time,omitempty"`        // 入职时间, 取值范围：`1` ～ `2147483647`,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeNo      string                                         `json:"employee_no,omitempty"`      // 工号,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeType    int64                                          `json:"employee_type,omitempty"`    // 员工类型, 可选值有: `1`：正式员工, `2`：实习生, `3`：外包, `4`：劳务, `5`：顾问,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Orders          []*EventV2ContactUserUpdatedV3ObjectOrder      `json:"orders,omitempty"`           // 用户排序信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	CustomAttrs     []*EventV2ContactUserUpdatedV3ObjectCustomAttr `json:"custom_attrs,omitempty"`     // 自定义属性,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
}

// EventV2ContactUserUpdatedV3ObjectAvatar ...
type EventV2ContactUserUpdatedV3ObjectAvatar struct {
	Avatar72     string `json:"avatar_72,omitempty"`     // 72*72像素头像链接
	Avatar240    string `json:"avatar_240,omitempty"`    // 240*240像素头像链接
	Avatar640    string `json:"avatar_640,omitempty"`    // 640*640像素头像链接
	AvatarOrigin string `json:"avatar_origin,omitempty"` // 原始头像链接
}

// EventV2ContactUserUpdatedV3ObjectStatus ...
type EventV2ContactUserUpdatedV3ObjectStatus struct {
	IsFrozen    bool `json:"is_frozen,omitempty"`    // 是否暂停
	IsResigned  bool `json:"is_resigned,omitempty"`  // 是否离职
	IsActivated bool `json:"is_activated,omitempty"` // 是否激活
	IsExited    bool `json:"is_exited,omitempty"`    // 是否主动退出，主动退出一段时间后用户会自动转为已离职
	IsUnjoin    bool `json:"is_unjoin,omitempty"`    // 是否未加入，需要用户自主确认才能加入团队
}

// EventV2ContactUserUpdatedV3ObjectOrder ...
type EventV2ContactUserUpdatedV3ObjectOrder struct {
	DepartmentID    string `json:"department_id,omitempty"`    // 排序信息对应的部门ID, ID值与查询参数中的department_id_type 对应。,不同 ID 的说明参见 [部门ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview)
	UserOrder       int64  `json:"user_order,omitempty"`       // 用户在其直属部门内的排序，数值越大，排序越靠前
	DepartmentOrder int64  `json:"department_order,omitempty"` // 用户所属的多个部门间的排序，数值越大，排序越靠前
}

// EventV2ContactUserUpdatedV3ObjectCustomAttr ...
type EventV2ContactUserUpdatedV3ObjectCustomAttr struct {
	Type  string                                            `json:"type,omitempty"`  // 自定义字段类型   , `TEXT`：文本, `HREF`：网页, `ENUMERATION`：枚举, `PICTURE_ENUM`：图片, `GENERIC_USER`：用户,[自定义字段相关常见问题](https://open.feishu.cn/document/ugTN1YjL4UTN24CO1UjN/uQzN1YjL0cTN24CN3UjN)
	ID    string                                            `json:"id,omitempty"`    // 自定义字段ID
	Value *EventV2ContactUserUpdatedV3ObjectCustomAttrValue `json:"value,omitempty"` // 自定义字段取值
}

// EventV2ContactUserUpdatedV3ObjectCustomAttrValue ...
type EventV2ContactUserUpdatedV3ObjectCustomAttrValue struct {
	Text        string                                                       `json:"text,omitempty"`         // 字段类型为`TEXT`时该参数定义字段值，必填；字段类型为`HREF`时该参数定义网页标题，必填
	URL         string                                                       `json:"url,omitempty"`          // 字段类型为 HREF 时，该参数定义默认 URL
	PcURL       string                                                       `json:"pc_url,omitempty"`       // 字段类型为 HREF 时，该参数定义PC端 URL
	OptionID    string                                                       `json:"option_id,omitempty"`    // 字段类型为 ENUMERATION 或 PICTURE_ENUM 时，该参数定义选项值
	OptionValue string                                                       `json:"option_value,omitempty"` // 选项值
	Name        string                                                       `json:"name,omitempty"`         // 名称
	PictureURL  string                                                       `json:"picture_url,omitempty"`  // 图片链接
	GenericUser *EventV2ContactUserUpdatedV3ObjectCustomAttrValueGenericUser `json:"generic_user,omitempty"` // 字段类型为 GENERIC_USER 时，该参数定义引用人员
}

// EventV2ContactUserUpdatedV3ObjectCustomAttrValueGenericUser ...
type EventV2ContactUserUpdatedV3ObjectCustomAttrValueGenericUser struct {
	ID   string `json:"id,omitempty"`   // 用户的user_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Type int64  `json:"type,omitempty"` // 用户类型    1：用户
}

// EventV2ContactUserUpdatedV3OldObject ...
type EventV2ContactUserUpdatedV3OldObject struct {
	OpenID        string                                            `json:"open_id,omitempty"`        // 用户的open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UnionID       string                                            `json:"union_id,omitempty"`       // 用户的union_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	UserID        string                                            `json:"user_id,omitempty"`        // 租户内用户的唯一标识 [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction), 字段权限要求: 获取用户 user ID
	Name          string                                            `json:"name,omitempty"`           // 用户名, 最小长度：`1` 字符,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	EnName        string                                            `json:"en_name,omitempty"`        // 英文名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Nickname      string                                            `json:"nickname,omitempty"`       // 别名,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Email         string                                            `json:"email,omitempty"`          // 邮箱, 字段权限要求: 获取用户邮箱信息
	JobTitle      string                                            `json:"job_title,omitempty"`      // 职务
	Mobile        string                                            `json:"mobile,omitempty"`         // 手机号, 字段权限要求: 获取用户手机号
	Gender        int64                                             `json:"gender,omitempty"`         // 性别, 可选值有: `0`：未知, `1`：男, `2`：女,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户性别,以应用身份访问通讯录,读取通讯录
	Avatar        *EventV2ContactUserUpdatedV3OldObjectAvatar       `json:"avatar,omitempty"`         // 用户头像信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户基本信息,以应用身份访问通讯录,读取通讯录
	Status        *EventV2ContactUserUpdatedV3OldObjectStatus       `json:"status,omitempty"`         // 用户状态,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	DepartmentIDs []string                                          `json:"department_ids,omitempty"` // 用户所属部门的ID列表,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	LeaderUserID  string                                            `json:"leader_user_id,omitempty"` // 用户的直接主管的用户open_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction),**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	City          string                                            `json:"city,omitempty"`           // 城市,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Country       string                                            `json:"country,omitempty"`        // 国家,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	WorkStation   string                                            `json:"work_station,omitempty"`   // 工位,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	JoinTime      int64                                             `json:"join_time,omitempty"`      // 入职时间, 取值范围：`1` ～ `2147483647`,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeNo    string                                            `json:"employee_no,omitempty"`    // 工号,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	EmployeeType  int64                                             `json:"employee_type,omitempty"`  // 员工类型, 可选值有: `1`：正式员工, `2`：实习生, `3`：外包, `4`：劳务, `5`：顾问,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
	Orders        []*EventV2ContactUserUpdatedV3OldObjectOrder      `json:"orders,omitempty"`         // 用户排序信息,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户组织架构信息,以应用身份访问通讯录,读取通讯录
	CustomAttrs   []*EventV2ContactUserUpdatedV3OldObjectCustomAttr `json:"custom_attrs,omitempty"`   // 自定义属性,**字段权限要求（满足任一）**：,以应用身份读取通讯录,获取用户雇佣信息,以应用身份访问通讯录,读取通讯录
}

// EventV2ContactUserUpdatedV3OldObjectAvatar ...
type EventV2ContactUserUpdatedV3OldObjectAvatar struct {
	Avatar72     string `json:"avatar_72,omitempty"`     // 72*72像素头像链接
	Avatar240    string `json:"avatar_240,omitempty"`    // 240*240像素头像链接
	Avatar640    string `json:"avatar_640,omitempty"`    // 640*640像素头像链接
	AvatarOrigin string `json:"avatar_origin,omitempty"` // 原始头像链接
}

// EventV2ContactUserUpdatedV3OldObjectStatus ...
type EventV2ContactUserUpdatedV3OldObjectStatus struct {
	IsFrozen    bool `json:"is_frozen,omitempty"`    // 是否暂停
	IsResigned  bool `json:"is_resigned,omitempty"`  // 是否离职
	IsActivated bool `json:"is_activated,omitempty"` // 是否激活
	IsExited    bool `json:"is_exited,omitempty"`    // 是否主动退出，主动退出一段时间后用户会自动转为已离职
	IsUnjoin    bool `json:"is_unjoin,omitempty"`    // 是否未加入，需要用户自主确认才能加入团队
}

// EventV2ContactUserUpdatedV3OldObjectOrder ...
type EventV2ContactUserUpdatedV3OldObjectOrder struct {
	DepartmentID    string `json:"department_id,omitempty"`    // 排序信息对应的部门ID, ID值与查询参数中的department_id_type 对应。,不同 ID 的说明参见 [部门ID说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/contact-v3/department/field-overview)
	UserOrder       int64  `json:"user_order,omitempty"`       // 用户在其直属部门内的排序，数值越大，排序越靠前
	DepartmentOrder int64  `json:"department_order,omitempty"` // 用户所属的多个部门间的排序，数值越大，排序越靠前
}

// EventV2ContactUserUpdatedV3OldObjectCustomAttr ...
type EventV2ContactUserUpdatedV3OldObjectCustomAttr struct {
	Type  string                                               `json:"type,omitempty"`  // 自定义字段类型   , `TEXT`：文本, `HREF`：网页, `ENUMERATION`：枚举, `PICTURE_ENUM`：图片, `GENERIC_USER`：用户,[自定义字段相关常见问题](https://open.feishu.cn/document/ugTN1YjL4UTN24CO1UjN/uQzN1YjL0cTN24CN3UjN)
	ID    string                                               `json:"id,omitempty"`    // 自定义字段ID
	Value *EventV2ContactUserUpdatedV3OldObjectCustomAttrValue `json:"value,omitempty"` // 自定义字段取值
}

// EventV2ContactUserUpdatedV3OldObjectCustomAttrValue ...
type EventV2ContactUserUpdatedV3OldObjectCustomAttrValue struct {
	Text        string                                                          `json:"text,omitempty"`         // 字段类型为`TEXT`时该参数定义字段值，必填；字段类型为`HREF`时该参数定义网页标题，必填
	URL         string                                                          `json:"url,omitempty"`          // 字段类型为 HREF 时，该参数定义默认 URL
	PcURL       string                                                          `json:"pc_url,omitempty"`       // 字段类型为 HREF 时，该参数定义PC端 URL
	OptionID    string                                                          `json:"option_id,omitempty"`    // 字段类型为 ENUMERATION 或 PICTURE_ENUM 时，该参数定义选项值
	OptionValue string                                                          `json:"option_value,omitempty"` // 选项值
	Name        string                                                          `json:"name,omitempty"`         // 名称
	PictureURL  string                                                          `json:"picture_url,omitempty"`  // 图片链接
	GenericUser *EventV2ContactUserUpdatedV3OldObjectCustomAttrValueGenericUser `json:"generic_user,omitempty"` // 字段类型为 GENERIC_USER 时，该参数定义引用人员
}

// EventV2ContactUserUpdatedV3OldObjectCustomAttrValueGenericUser ...
type EventV2ContactUserUpdatedV3OldObjectCustomAttrValueGenericUser struct {
	ID   string `json:"id,omitempty"`   // 用户的user_id [用户相关的 ID 概念](https://open.feishu.cn/document/home/<USER>/introduction)
	Type int64  `json:"type,omitempty"` // 用户类型    1：用户
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2DriveFileBitableRecordChangedV1
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 被订阅的多维表格记录发生变更将会触发此事件。
// ## 概述
// :::html
// <md-table>
// <md-thead>
// <tr>
// <md-th>基本</md-th>
// <md-th></md-th>
// </tr>
// </md-thead>
// <md-tbody>
// <md-tr>
// <md-th>支持的应用类型</md-th>
// <md-td>
// <md-app-support types="custom,isv"></md-app-support>
// </md-td>
// </md-tr>
// <md-tr>
// <md-th>
// 权限要求
// <md-tooltip type="info">调用该 API 所需的权限。开启其中任意一项权限即可调用</md-tooltip>
// <div style="color: rgb(100, 106, 115);font-size: 12px;line-height: 20px;white-space: pre-line;font-weight: 500;padding-top: 4px;">开启任一权限即可</div>
// </md-th>
// <md-td>
// 查看、评论、编辑和管理多维表格
// 查看、评论、编辑和管理云空间中所有文件
// </md-td>
// </md-tr>
// </md-tbody>
// </md-table>
// :::
// 如何订阅文档请点击查看 [订阅云文档事件](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/drive-v1/file/subscribe)。
// ## 支持的记录变更类型
// | 变更类型         | action           |
// | --------- | --------------- |
// |新增行记录 | `record_added` |
// |删除行记录 | `record_deleted` |
// |修改行记录 | `record_edited` |
// 回调结构中的 `field_value` 字段为 JSON 序列化后的字符串，序列化前的结构请查看 [数据结构](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/bitable/development-guide/bitable-structure)
// ## 回调示例
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/drive-v1/event/list/bitable-record-changed
func (r *EventCallbackService) HandlerEventV2DriveFileBitableRecordChangedV1(f EventV2DriveFileBitableRecordChangedV1Handler) {
	r.cli.eventHandler.eventV2DriveFileBitableRecordChangedV1Handler = f
}

// EventV2DriveFileBitableRecordChangedV1Handler event EventV2DriveFileBitableRecordChangedV1 handler
type EventV2DriveFileBitableRecordChangedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2DriveFileBitableRecordChangedV1) (string, error)

// EventV2DriveFileBitableRecordChangedV1 ...
type EventV2DriveFileBitableRecordChangedV1 struct {
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2DriveFileDeletedV1
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 文件被彻底删除将触发此事件。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/event/file-deleted-completely
func (r *EventCallbackService) HandlerEventV2DriveFileDeletedV1(f EventV2DriveFileDeletedV1Handler) {
	r.cli.eventHandler.eventV2DriveFileDeletedV1Handler = f
}

// EventV2DriveFileDeletedV1Handler event EventV2DriveFileDeletedV1 handler
type EventV2DriveFileDeletedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2DriveFileDeletedV1) (string, error)

// EventV2DriveFileDeletedV1 ...
type EventV2DriveFileDeletedV1 struct {
	FileToken  string                               `json:"file_token,omitempty"`  // 文件token. 如: doccnxxxxxx
	FileType   FileType                             `json:"file_type,omitempty"`   // 文件类型，目前有doc、sheet. 如: doc
	OperatorID *EventV2DriveFileDeletedV1OperatorID `json:"operator_id,omitempty"` // 操作者id
}

// EventV2DriveFileDeletedV1OperatorID ...
type EventV2DriveFileDeletedV1OperatorID struct {
	OpenID  string `json:"open_id,omitempty"`  // 如: ou_xxxxxx
	UnionID string `json:"union_id,omitempty"` // 如: on_xxxxxx
	UserID  string `json:"user_id,omitempty"`  // 如: xxxxxx
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2DriveFileEditV1
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 文件编辑将触发此事件。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/event/file-edited
func (r *EventCallbackService) HandlerEventV2DriveFileEditV1(f EventV2DriveFileEditV1Handler) {
	r.cli.eventHandler.eventV2DriveFileEditV1Handler = f
}

// EventV2DriveFileEditV1Handler event EventV2DriveFileEditV1 handler
type EventV2DriveFileEditV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2DriveFileEditV1) (string, error)

// EventV2DriveFileEditV1 ...
type EventV2DriveFileEditV1 struct {
	FileToken        string                                `json:"file_token,omitempty"`         // 出现编辑的文档token. 如: doccnxxxxxxxxxxxxxxxxxxxxxxx
	FileType         FileType                              `json:"file_type,omitempty"`          // 出现编辑的文档类型. 如: doc
	OperatorIDList   []*EventV2DriveFileEditV1OperatorID   `json:"operator_id_list,omitempty"`   // 编辑人列表
	SubscriberIDList []*EventV2DriveFileEditV1SubscriberID `json:"subscriber_id_list,omitempty"` // 订阅用户列表
}

// EventV2DriveFileEditV1OperatorID ...
type EventV2DriveFileEditV1OperatorID struct {
	OpenID  string `json:"open_id,omitempty"`  // 如: ou_xxxxxxxxxxxxxxxxxxxxxxxxx
	UnionID string `json:"union_id,omitempty"` // 如: on_xxxxxxxxxxxxxxxxxxxxxxxxx
	UserID  string `json:"user_id,omitempty"`  // 如: xxxxxxxx
}

// EventV2DriveFileEditV1SubscriberID ...
type EventV2DriveFileEditV1SubscriberID struct {
	OpenID  string `json:"open_id,omitempty"`  // 如: ou_xxxxxxxxxxxxxxxxxxxxxxxxx
	UnionID string `json:"union_id,omitempty"` // 如: on_xxxxxxxxxxxxxxxxxxxxxxxxx
	UserID  string `json:"user_id,omitempty"`  // 如: xxxxxxxx
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2DriveFilePermissionMemberAddedV1
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 文件协作者添加用户/群时将触发此事件。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/event/file-collaborator-add
func (r *EventCallbackService) HandlerEventV2DriveFilePermissionMemberAddedV1(f EventV2DriveFilePermissionMemberAddedV1Handler) {
	r.cli.eventHandler.eventV2DriveFilePermissionMemberAddedV1Handler = f
}

// EventV2DriveFilePermissionMemberAddedV1Handler event EventV2DriveFilePermissionMemberAddedV1 handler
type EventV2DriveFilePermissionMemberAddedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2DriveFilePermissionMemberAddedV1) (string, error)

// EventV2DriveFilePermissionMemberAddedV1 ...
type EventV2DriveFilePermissionMemberAddedV1 struct {
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2DriveFilePermissionMemberRemovedV1
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 文件协作者移除用户/群时将触发此事件。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/event/file-collaborator-remove
func (r *EventCallbackService) HandlerEventV2DriveFilePermissionMemberRemovedV1(f EventV2DriveFilePermissionMemberRemovedV1Handler) {
	r.cli.eventHandler.eventV2DriveFilePermissionMemberRemovedV1Handler = f
}

// EventV2DriveFilePermissionMemberRemovedV1Handler event EventV2DriveFilePermissionMemberRemovedV1 handler
type EventV2DriveFilePermissionMemberRemovedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2DriveFilePermissionMemberRemovedV1) (string, error)

// EventV2DriveFilePermissionMemberRemovedV1 ...
type EventV2DriveFilePermissionMemberRemovedV1 struct {
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2DriveFileReadV1
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 文件被打开将触发此事件。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/event/file-read
func (r *EventCallbackService) HandlerEventV2DriveFileReadV1(f EventV2DriveFileReadV1Handler) {
	r.cli.eventHandler.eventV2DriveFileReadV1Handler = f
}

// EventV2DriveFileReadV1Handler event EventV2DriveFileReadV1 handler
type EventV2DriveFileReadV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2DriveFileReadV1) (string, error)

// EventV2DriveFileReadV1 ...
type EventV2DriveFileReadV1 struct {
	FileToken      string                              `json:"file_token,omitempty"` // 文件token. 如: doccnxxxxxx
	FileType       FileType                            `json:"file_type,omitempty"`  // 文件类型，目前有doc、sheet. 如: doc
	OperatorIDList []*EventV2DriveFileReadV1OperatorID `json:"operator_id_list,omitempty"`
}

// EventV2DriveFileReadV1OperatorID ...
type EventV2DriveFileReadV1OperatorID struct {
	OpenID  string `json:"open_id,omitempty"`  // 如: ou_xxxxxx
	UnionID string `json:"union_id,omitempty"` // 如: on_xxxxxx
	UserID  string `json:"user_id,omitempty"`  // 如: xxxxxx
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2DriveFileTitleUpdatedV1
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 文件标题变更时将触发此事件。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/event/file-title-update
func (r *EventCallbackService) HandlerEventV2DriveFileTitleUpdatedV1(f EventV2DriveFileTitleUpdatedV1Handler) {
	r.cli.eventHandler.eventV2DriveFileTitleUpdatedV1Handler = f
}

// EventV2DriveFileTitleUpdatedV1Handler event EventV2DriveFileTitleUpdatedV1 handler
type EventV2DriveFileTitleUpdatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2DriveFileTitleUpdatedV1) (string, error)

// EventV2DriveFileTitleUpdatedV1 ...
type EventV2DriveFileTitleUpdatedV1 struct {
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2DriveFileTrashedV1
//
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
// 文件被删除到回收站将触发此事件。
//
// doc: https://open.feishu.cn/document/ukTMukTMukTM/uUDN04SN0QjL1QDN/event/delete-file-to-trash-can
func (r *EventCallbackService) HandlerEventV2DriveFileTrashedV1(f EventV2DriveFileTrashedV1Handler) {
	r.cli.eventHandler.eventV2DriveFileTrashedV1Handler = f
}

// EventV2DriveFileTrashedV1Handler event EventV2DriveFileTrashedV1 handler
type EventV2DriveFileTrashedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2DriveFileTrashedV1) (string, error)

// EventV2DriveFileTrashedV1 ...
type EventV2DriveFileTrashedV1 struct {
	FileToken  string                               `json:"file_token,omitempty"`  // 文件token. 如: doccnxxxxxx
	FileType   FileType                             `json:"file_type,omitempty"`   // 文件类型，目前有doc、sheet. 如: doc
	OperatorID *EventV2DriveFileTrashedV1OperatorID `json:"operator_id,omitempty"` // 操作者id
}

// EventV2DriveFileTrashedV1OperatorID ...
type EventV2DriveFileTrashedV1OperatorID struct {
	OpenID  string `json:"open_id,omitempty"`  // 如: ou_xxxxxx
	UnionID string `json:"union_id,omitempty"` // 如: on_xxxxxx
	UserID  string `json:"user_id,omitempty"`  // 如: xxxxxx
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2HelpdeskNotificationApproveV1
//
// Push审核状态通知事件{使用示例}(url=/api/tools/api_explore/api_explore_config?project=helpdesk&version=v1&resource=notification&event=approve)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/notification/events/approve
func (r *EventCallbackService) HandlerEventV2HelpdeskNotificationApproveV1(f EventV2HelpdeskNotificationApproveV1Handler) {
	r.cli.eventHandler.eventV2HelpdeskNotificationApproveV1Handler = f
}

// EventV2HelpdeskNotificationApproveV1Handler event EventV2HelpdeskNotificationApproveV1 handler
type EventV2HelpdeskNotificationApproveV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2HelpdeskNotificationApproveV1) (string, error)

// EventV2HelpdeskNotificationApproveV1 ...
type EventV2HelpdeskNotificationApproveV1 struct {
	NotificationID string `json:"notification_id,omitempty"` // 推送任务唯一ID
	HelpdeskID     string `json:"helpdesk_id,omitempty"`     // 服务台唯一ID
	ApproveStatus  string `json:"approve_status,omitempty"`  // REJECTED(审核不通过),APPROVED(审核通过),CANCELED(取消审核),DELETED(删除审核)
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2HelpdeskTicketCreatedV1
//
// 可监听服务台的工单创建事件。需使用订阅接口订阅：[事件订阅](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/event/overview)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created
func (r *EventCallbackService) HandlerEventV2HelpdeskTicketCreatedV1(f EventV2HelpdeskTicketCreatedV1Handler) {
	r.cli.eventHandler.eventV2HelpdeskTicketCreatedV1Handler = f
}

// EventV2HelpdeskTicketCreatedV1Handler event EventV2HelpdeskTicketCreatedV1 handler
type EventV2HelpdeskTicketCreatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2HelpdeskTicketCreatedV1) (string, error)

// EventV2HelpdeskTicketCreatedV1 ...
type EventV2HelpdeskTicketCreatedV1 struct {
	TicketID   string                               `json:"ticket_id,omitempty"`   // 工单ID,[可以从工单列表里面取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/list),[也可以订阅工单创建事件获取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created)
	HelpdeskID string                               `json:"helpdesk_id,omitempty"` // 服务台ID
	Guest      *EventV2HelpdeskTicketCreatedV1Guest `json:"guest,omitempty"`       // 工单创建用户
	Stage      int64                                `json:"stage,omitempty"`       // 工单阶段，1：bot，2：人工
	Status     int64                                `json:"status,omitempty"`      // 工单状态，1：已创建 2: 处理中 3: 排队中 4：待定 5：待用户响应 50: 被机器人关闭 51: 被客服关闭 52: 用户自己关闭
	Score      int64                                `json:"score,omitempty"`       // 工单评分，1：不满意，2:一般，3:满意
	CreatedAt  int64                                `json:"created_at,omitempty"`  // 工单创建时间
	UpdatedAt  int64                                `json:"updated_at,omitempty"`  // 工单更新时间，没有值时为-1
	ClosedAt   int64                                `json:"closed_at,omitempty"`   // 工单结束时间
	Channel    int64                                `json:"channel,omitempty"`     // 工单渠道，描述：,9：Open API 2：二维码 14：分享 13：搜索 其他数字：其他渠道
	Solve      int64                                `json:"solve,omitempty"`       // 工单是否解决 1:没解决 2:已解决
	ChatID     string                               `json:"chat_id,omitempty"`     // 会话Open ID
}

// EventV2HelpdeskTicketCreatedV1Guest ...
type EventV2HelpdeskTicketCreatedV1Guest struct {
	ID   *EventV2HelpdeskTicketCreatedV1GuestID `json:"id,omitempty"`   // 用户 ID
	Name string                                 `json:"name,omitempty"` // 用户名
}

// EventV2HelpdeskTicketCreatedV1GuestID ...
type EventV2HelpdeskTicketCreatedV1GuestID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2HelpdeskTicketMessageCreatedV1
//
// 该消息事件属于工单消息事件。需使用订阅接口订阅：[事件订阅](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/event/overview)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket_message/events/created
func (r *EventCallbackService) HandlerEventV2HelpdeskTicketMessageCreatedV1(f EventV2HelpdeskTicketMessageCreatedV1Handler) {
	r.cli.eventHandler.eventV2HelpdeskTicketMessageCreatedV1Handler = f
}

// EventV2HelpdeskTicketMessageCreatedV1Handler event EventV2HelpdeskTicketMessageCreatedV1 handler
type EventV2HelpdeskTicketMessageCreatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2HelpdeskTicketMessageCreatedV1) (string, error)

// EventV2HelpdeskTicketMessageCreatedV1 ...
type EventV2HelpdeskTicketMessageCreatedV1 struct {
	TicketMessageID string                                         `json:"ticket_message_id,omitempty"` // 工单消息ID
	MessageID       string                                         `json:"message_id,omitempty"`        // chat消息open ID
	MsgType         MsgType                                        `json:"msg_type,omitempty"`          // 消息类型；text：纯文本
	Position        string                                         `json:"position,omitempty"`          // 消息位置
	SenderID        *EventV2HelpdeskTicketMessageCreatedV1SenderID `json:"sender_id,omitempty"`         // 用户 ID
	SenderType      int64                                          `json:"sender_type,omitempty"`       // 发送者类型 1：机器人；2：用户；3：客服
	Text            string                                         `json:"text,omitempty"`              // 内容
	Ticket          *EventV2HelpdeskTicketMessageCreatedV1Ticket   `json:"ticket,omitempty"`            // 工单信息
	EventID         string                                         `json:"event_id,omitempty"`          // 消息事件ID
	ChatID          string                                         `json:"chat_id,omitempty"`           // 会话ID
	Content         *EventV2HelpdeskTicketMessageCreatedV1Content  `json:"content,omitempty"`           // 内容详情
}

// EventV2HelpdeskTicketMessageCreatedV1SenderID ...
type EventV2HelpdeskTicketMessageCreatedV1SenderID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2HelpdeskTicketMessageCreatedV1Ticket ...
type EventV2HelpdeskTicketMessageCreatedV1Ticket struct {
	TicketID string `json:"ticket_id,omitempty"` // 工单ID,[可以从工单列表里面取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/list),[也可以订阅工单创建事件获取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created)
	Stage    int64  `json:"stage,omitempty"`     // 工单阶段，1：bot，2：人工
	Status   int64  `json:"status,omitempty"`    // 工单状态，1：已创建 2: 处理中 3: 排队中 4：待定 5：待用户响应 50: 被机器人关闭 51: 被客服关闭 52: 用户自己关闭
}

// EventV2HelpdeskTicketMessageCreatedV1Content ...
type EventV2HelpdeskTicketMessageCreatedV1Content struct {
	Content   string   `json:"content,omitempty"`    // 内容
	MsgType   MsgType  `json:"msg_type,omitempty"`   // 消息类型；text：纯文本；post：富文本；image：图片
	ImageKeys []string `json:"image_keys,omitempty"` // 图片ID
	ImageKey  string   `json:"image_key,omitempty"`  // 图片ID
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2HelpdeskTicketUpdatedV1
//
// 可监听工单状态和阶段变更事件。需使用订阅接口订阅：[事件订阅](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/event/overview)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/updated
func (r *EventCallbackService) HandlerEventV2HelpdeskTicketUpdatedV1(f EventV2HelpdeskTicketUpdatedV1Handler) {
	r.cli.eventHandler.eventV2HelpdeskTicketUpdatedV1Handler = f
}

// EventV2HelpdeskTicketUpdatedV1Handler event EventV2HelpdeskTicketUpdatedV1 handler
type EventV2HelpdeskTicketUpdatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2HelpdeskTicketUpdatedV1) (string, error)

// EventV2HelpdeskTicketUpdatedV1 ...
type EventV2HelpdeskTicketUpdatedV1 struct {
	Object    *EventV2HelpdeskTicketUpdatedV1Object    `json:"object,omitempty"`     // ticket after update
	OldObject *EventV2HelpdeskTicketUpdatedV1OldObject `json:"old_object,omitempty"` // ticket before update, only has updated fields
}

// EventV2HelpdeskTicketUpdatedV1Object ...
type EventV2HelpdeskTicketUpdatedV1Object struct {
	TicketID   string                                     `json:"ticket_id,omitempty"`   // 工单ID,[可以从工单列表里面取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/list),[也可以订阅工单创建事件获取](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/helpdesk-v1/ticket/events/created)
	HelpdeskID string                                     `json:"helpdesk_id,omitempty"` // 服务台ID
	Guest      *EventV2HelpdeskTicketUpdatedV1ObjectGuest `json:"guest,omitempty"`       // 工单创建用户
	Stage      int64                                      `json:"stage,omitempty"`       // 工单阶段，1：bot，2：人工
	Status     int64                                      `json:"status,omitempty"`      // 工单状态，1：已创建 2: 处理中 3: 排队中 4：待定 5：待用户响应 50: 被机器人关闭 51: 被客服关闭 52: 用户自己关闭
	Score      int64                                      `json:"score,omitempty"`       // 工单评分，1：不满意，2:一般，3:满意
	CreatedAt  int64                                      `json:"created_at,omitempty"`  // 工单创建时间
	UpdatedAt  int64                                      `json:"updated_at,omitempty"`  // 工单更新时间，没有值时为-1
	ClosedAt   int64                                      `json:"closed_at,omitempty"`   // 工单结束时间
	Channel    int64                                      `json:"channel,omitempty"`     // 工单渠道，描述：,9：Open API 2：二维码 14：分享 13：搜索 其他数字：其他渠道
	Solve      int64                                      `json:"solve,omitempty"`       // 工单是否解决 1:没解决 2:已解决
	ChatID     string                                     `json:"chat_id,omitempty"`     // 会话Open ID
}

// EventV2HelpdeskTicketUpdatedV1ObjectGuest ...
type EventV2HelpdeskTicketUpdatedV1ObjectGuest struct {
	ID   *EventV2HelpdeskTicketUpdatedV1ObjectGuestID `json:"id,omitempty"`   // 用户 ID
	Name string                                       `json:"name,omitempty"` // 用户名
}

// EventV2HelpdeskTicketUpdatedV1ObjectGuestID ...
type EventV2HelpdeskTicketUpdatedV1ObjectGuestID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2HelpdeskTicketUpdatedV1OldObject ...
type EventV2HelpdeskTicketUpdatedV1OldObject struct {
	Stage     int64 `json:"stage,omitempty"`      // ticket stage
	Status    int64 `json:"status,omitempty"`     // ticket status
	UpdatedAt int64 `json:"updated_at,omitempty"` // ticket update time
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMChatDisbandedV1
//
// 群组被解散后触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=chat&event=disbanded)
// 注意事项：
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)
// - 需要订阅 [消息与群组] 分类下的 [解散群] 事件
// - 事件会向群内订阅了该事件的机器人进行推送
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat/events/disbanded
func (r *EventCallbackService) HandlerEventV2IMChatDisbandedV1(f EventV2IMChatDisbandedV1Handler) {
	r.cli.eventHandler.eventV2IMChatDisbandedV1Handler = f
}

// EventV2IMChatDisbandedV1Handler event EventV2IMChatDisbandedV1 handler
type EventV2IMChatDisbandedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMChatDisbandedV1) (string, error)

// EventV2IMChatDisbandedV1 ...
type EventV2IMChatDisbandedV1 struct {
	ChatID            string                              `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorID        *EventV2IMChatDisbandedV1OperatorID `json:"operator_id,omitempty"`         // 用户 ID
	External          bool                                `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey string                              `json:"operator_tenant_key,omitempty"` // 操作者的租户 Key
}

// EventV2IMChatDisbandedV1OperatorID ...
type EventV2IMChatDisbandedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMChatMemberBotAddedV1
//
// 机器人被用户添加至群聊时触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=chat.member.bot&event=added)
// 注意事项：
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)
// - 需要订阅 [消息与群组] 分类下的 [机器人进群] 事件
// - 事件会向进群的机器人进行推送
// - 机器人邀请机器人不会触发事件
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-bot/events/added
func (r *EventCallbackService) HandlerEventV2IMChatMemberBotAddedV1(f EventV2IMChatMemberBotAddedV1Handler) {
	r.cli.eventHandler.eventV2IMChatMemberBotAddedV1Handler = f
}

// EventV2IMChatMemberBotAddedV1Handler event EventV2IMChatMemberBotAddedV1 handler
type EventV2IMChatMemberBotAddedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMChatMemberBotAddedV1) (string, error)

// EventV2IMChatMemberBotAddedV1 ...
type EventV2IMChatMemberBotAddedV1 struct {
	ChatID            string                                   `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorID        *EventV2IMChatMemberBotAddedV1OperatorID `json:"operator_id,omitempty"`         // 用户 ID
	External          bool                                     `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey string                                   `json:"operator_tenant_key,omitempty"` // operator tenant key
}

// EventV2IMChatMemberBotAddedV1OperatorID ...
type EventV2IMChatMemberBotAddedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMChatMemberBotDeletedV1
//
// 机器人被移出群聊后触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=chat.member.bot&event=deleted)
// 注意事项：
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)
// - 需要订阅 [消息与群组] 分类下的 [机器人被移出群] 事件
// - 事件会向被移出群的机器人进行推送
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-bot/events/deleted
func (r *EventCallbackService) HandlerEventV2IMChatMemberBotDeletedV1(f EventV2IMChatMemberBotDeletedV1Handler) {
	r.cli.eventHandler.eventV2IMChatMemberBotDeletedV1Handler = f
}

// EventV2IMChatMemberBotDeletedV1Handler event EventV2IMChatMemberBotDeletedV1 handler
type EventV2IMChatMemberBotDeletedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMChatMemberBotDeletedV1) (string, error)

// EventV2IMChatMemberBotDeletedV1 ...
type EventV2IMChatMemberBotDeletedV1 struct {
	ChatID            string                                     `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorID        *EventV2IMChatMemberBotDeletedV1OperatorID `json:"operator_id,omitempty"`         // 用户 ID
	External          bool                                       `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey string                                     `json:"operator_tenant_key,omitempty"` // 操作者租户 Key
}

// EventV2IMChatMemberBotDeletedV1OperatorID ...
type EventV2IMChatMemberBotDeletedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMChatMemberUserAddedV1
//
// 新用户进群触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=chat.member.user&event=added)
// 注意事项：
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)
// - 需要订阅 [消息与群组] 分类下的 [用户进群] 事件
// - 事件会向群内订阅了该事件的机器人进行推送
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-user/events/added
func (r *EventCallbackService) HandlerEventV2IMChatMemberUserAddedV1(f EventV2IMChatMemberUserAddedV1Handler) {
	r.cli.eventHandler.eventV2IMChatMemberUserAddedV1Handler = f
}

// EventV2IMChatMemberUserAddedV1Handler event EventV2IMChatMemberUserAddedV1 handler
type EventV2IMChatMemberUserAddedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMChatMemberUserAddedV1) (string, error)

// EventV2IMChatMemberUserAddedV1 ...
type EventV2IMChatMemberUserAddedV1 struct {
	ChatID            string                                    `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorID        *EventV2IMChatMemberUserAddedV1OperatorID `json:"operator_id,omitempty"`         // 用户 ID
	External          bool                                      `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey string                                    `json:"operator_tenant_key,omitempty"` // 操作者租户 Key
	Users             []*EventV2IMChatMemberUserAddedV1User     `json:"users,omitempty"`               // 被添加的用户列表
}

// EventV2IMChatMemberUserAddedV1OperatorID ...
type EventV2IMChatMemberUserAddedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2IMChatMemberUserAddedV1User ...
type EventV2IMChatMemberUserAddedV1User struct {
	Name      string                                    `json:"name,omitempty"`       // 用户名字
	TenantKey string                                    `json:"tenant_key,omitempty"` // 租户 Key
	UserID    *EventV2IMChatMemberUserAddedV1UserUserID `json:"user_id,omitempty"`    // 用户 ID
}

// EventV2IMChatMemberUserAddedV1UserUserID ...
type EventV2IMChatMemberUserAddedV1UserUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMChatMemberUserDeletedV1
//
// 用户主动退群或被移出群聊时推送事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=chat.member.user&event=deleted)
// 注意事项：
// - 应用需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)并且机器人所在群发生上述变化
// - 机器人需要订阅 [消息与群组] 分类下的 [用户主动退群或被移出群聊] 事件
// - 事件会向群内订阅了该事件的机器人进行推送
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-user/events/deleted
func (r *EventCallbackService) HandlerEventV2IMChatMemberUserDeletedV1(f EventV2IMChatMemberUserDeletedV1Handler) {
	r.cli.eventHandler.eventV2IMChatMemberUserDeletedV1Handler = f
}

// EventV2IMChatMemberUserDeletedV1Handler event EventV2IMChatMemberUserDeletedV1 handler
type EventV2IMChatMemberUserDeletedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMChatMemberUserDeletedV1) (string, error)

// EventV2IMChatMemberUserDeletedV1 ...
type EventV2IMChatMemberUserDeletedV1 struct {
	ChatID            string                                      `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorID        *EventV2IMChatMemberUserDeletedV1OperatorID `json:"operator_id,omitempty"`         // 用户 ID
	External          bool                                        `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey string                                      `json:"operator_tenant_key,omitempty"` // 操作者租户 Key
	Users             []*EventV2IMChatMemberUserDeletedV1User     `json:"users,omitempty"`               // 被移除用户列表
}

// EventV2IMChatMemberUserDeletedV1OperatorID ...
type EventV2IMChatMemberUserDeletedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2IMChatMemberUserDeletedV1User ...
type EventV2IMChatMemberUserDeletedV1User struct {
	Name      string                                      `json:"name,omitempty"`       // 用户名字
	TenantKey string                                      `json:"tenant_key,omitempty"` // 租户 Key
	UserID    *EventV2IMChatMemberUserDeletedV1UserUserID `json:"user_id,omitempty"`    // 用户 ID
}

// EventV2IMChatMemberUserDeletedV1UserUserID ...
type EventV2IMChatMemberUserDeletedV1UserUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMChatMemberUserWithdrawnV1
//
// 撤销拉用户进群后触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=chat.member.user&event=withdrawn)
// 注意事项：
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)
// - 需要订阅 [消息与群组] 分类下的 [撤销拉用户进群] 事件
// - 事件会向群内订阅了该事件的机器人进行推送
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-member-user/events/withdrawn
func (r *EventCallbackService) HandlerEventV2IMChatMemberUserWithdrawnV1(f EventV2IMChatMemberUserWithdrawnV1Handler) {
	r.cli.eventHandler.eventV2IMChatMemberUserWithdrawnV1Handler = f
}

// EventV2IMChatMemberUserWithdrawnV1Handler event EventV2IMChatMemberUserWithdrawnV1 handler
type EventV2IMChatMemberUserWithdrawnV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMChatMemberUserWithdrawnV1) (string, error)

// EventV2IMChatMemberUserWithdrawnV1 ...
type EventV2IMChatMemberUserWithdrawnV1 struct {
	ChatID            string                                        `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorID        *EventV2IMChatMemberUserWithdrawnV1OperatorID `json:"operator_id,omitempty"`         // 用户 ID
	External          bool                                          `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey string                                        `json:"operator_tenant_key,omitempty"` // operator tenant key
	Users             []*EventV2IMChatMemberUserWithdrawnV1User     `json:"users,omitempty"`               // 被撤销加群的用户列表
}

// EventV2IMChatMemberUserWithdrawnV1OperatorID ...
type EventV2IMChatMemberUserWithdrawnV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2IMChatMemberUserWithdrawnV1User ...
type EventV2IMChatMemberUserWithdrawnV1User struct {
	Name      string                                        `json:"name,omitempty"`       // 用户名字
	TenantKey string                                        `json:"tenant_key,omitempty"` // 租户 Key
	UserID    *EventV2IMChatMemberUserWithdrawnV1UserUserID `json:"user_id,omitempty"`    // 用户 ID
}

// EventV2IMChatMemberUserWithdrawnV1UserUserID ...
type EventV2IMChatMemberUserWithdrawnV1UserUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMChatUpdatedV1
//
// 群组配置被修改后触发此事件，包含：
// - 群主转移
// - 群基本信息修改(群头像/群名称/群描述/群国际化名称)
// - 群权限修改(加人入群权限/群编辑权限/at所有人权限/群分享权限)。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=chat&event=updated)
// 注意事项：
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)
// - 需要订阅 [消息与群组] 分类下的 [群配置修改] 事件
// - 事件会向群内订阅了该事件的机器人进行推送
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat/events/updated
func (r *EventCallbackService) HandlerEventV2IMChatUpdatedV1(f EventV2IMChatUpdatedV1Handler) {
	r.cli.eventHandler.eventV2IMChatUpdatedV1Handler = f
}

// EventV2IMChatUpdatedV1Handler event EventV2IMChatUpdatedV1 handler
type EventV2IMChatUpdatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMChatUpdatedV1) (string, error)

// EventV2IMChatUpdatedV1 ...
type EventV2IMChatUpdatedV1 struct {
	ChatID            string                               `json:"chat_id,omitempty"`             // 群组 ID，详情参见[群ID 说明](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-id-description)
	OperatorID        *EventV2IMChatUpdatedV1OperatorID    `json:"operator_id,omitempty"`         // 用户 ID
	External          bool                                 `json:"external,omitempty"`            // 是否是外部群
	OperatorTenantKey string                               `json:"operator_tenant_key,omitempty"` // 操作者租户 Key
	AfterChange       *EventV2IMChatUpdatedV1AfterChange   `json:"after_change,omitempty"`        // 更新后的群信息
	BeforeChange      *EventV2IMChatUpdatedV1BeforeChange  `json:"before_change,omitempty"`       // 更新前的群信息
	ModeratorList     *EventV2IMChatUpdatedV1ModeratorList `json:"moderator_list,omitempty"`      // 群可发言成员名单的变更信息
}

// EventV2IMChatUpdatedV1OperatorID ...
type EventV2IMChatUpdatedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2IMChatUpdatedV1AfterChange ...
type EventV2IMChatUpdatedV1AfterChange struct {
	Avatar                 string                                    `json:"avatar,omitempty"`                   // 群头像
	Name                   string                                    `json:"name,omitempty"`                     // 群名称
	Description            string                                    `json:"description,omitempty"`              // 群描述
	I18nNames              *I18nNames                                `json:"i18n_names,omitempty"`               // 群国际化名称
	AddMemberPermission    AddMemberPermission                       `json:"add_member_permission,omitempty"`    // 加人入群权限(all_members/only_owner/unknown)
	ShareCardPermission    ShareCardPermission                       `json:"share_card_permission,omitempty"`    // 群分享权限(allowed/not_allowed/unknown)
	AtAllPermission        AtAllPermission                           `json:"at_all_permission,omitempty"`        // at 所有人权限(all_members/only_owner/unknown)
	EditPermission         EditPermission                            `json:"edit_permission,omitempty"`          // 群编辑权限(all_members/only_owner/unknown)
	MembershipApproval     MembershipApproval                        `json:"membership_approval,omitempty"`      // 加群审批(no_approval_required/approval_required)
	JoinMessageVisibility  MessageVisibility                         `json:"join_message_visibility,omitempty"`  // 入群消息可见性(only_owner/all_members/not_anyone)
	LeaveMessageVisibility MessageVisibility                         `json:"leave_message_visibility,omitempty"` // 出群消息可见性(only_owner/all_members/not_anyone)
	ModerationPermission   ModerationPermission                      `json:"moderation_permission,omitempty"`    // 发言权限(all_members/only_owner)
	OwnerID                *EventV2IMChatUpdatedV1AfterChangeOwnerID `json:"owner_id,omitempty"`                 // 用户 ID
}

// EventV2IMChatUpdatedV1AfterChangeOwnerID ...
type EventV2IMChatUpdatedV1AfterChangeOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2IMChatUpdatedV1BeforeChange ...
type EventV2IMChatUpdatedV1BeforeChange struct {
	Avatar                 string                                     `json:"avatar,omitempty"`                   // 群头像
	Name                   string                                     `json:"name,omitempty"`                     // 群名称
	Description            string                                     `json:"description,omitempty"`              // 群描述
	I18nNames              *I18nNames                                 `json:"i18n_names,omitempty"`               // 群国际化名称
	AddMemberPermission    AddMemberPermission                        `json:"add_member_permission,omitempty"`    // 加人入群权限(all_members/only_owner/unknown)
	ShareCardPermission    ShareCardPermission                        `json:"share_card_permission,omitempty"`    // 群分享权限(allowed/not_allowed/unknown)
	AtAllPermission        AtAllPermission                            `json:"at_all_permission,omitempty"`        // at 所有人权限(all_members/only_owner/unknown)
	EditPermission         EditPermission                             `json:"edit_permission,omitempty"`          // 群编辑权限(all_members/only_owner/unknown)
	MembershipApproval     MembershipApproval                         `json:"membership_approval,omitempty"`      // 加群审批(no_approval_required/approval_required)
	JoinMessageVisibility  MessageVisibility                          `json:"join_message_visibility,omitempty"`  // 入群消息可见性(only_owner/all_members/not_anyone)
	LeaveMessageVisibility MessageVisibility                          `json:"leave_message_visibility,omitempty"` // 出群消息可见性(only_owner/all_members/not_anyone)
	ModerationPermission   ModerationPermission                       `json:"moderation_permission,omitempty"`    // 发言权限(all_members/only_owner)
	OwnerID                *EventV2IMChatUpdatedV1BeforeChangeOwnerID `json:"owner_id,omitempty"`                 // 用户 ID
}

// EventV2IMChatUpdatedV1BeforeChangeOwnerID ...
type EventV2IMChatUpdatedV1BeforeChangeOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2IMChatUpdatedV1ModeratorList ...
type EventV2IMChatUpdatedV1ModeratorList struct {
	AddedMemberList   []*EventV2IMChatUpdatedV1ModeratorListAddedMember   `json:"added_member_list,omitempty"`   // 被添加进可发言名单的用户列表（列表中一定会有owner）
	RemovedMemberList []*EventV2IMChatUpdatedV1ModeratorListRemovedMember `json:"removed_member_list,omitempty"` // 被移除出可发言名单的用户列表
}

// EventV2IMChatUpdatedV1ModeratorListAddedMember ...
type EventV2IMChatUpdatedV1ModeratorListAddedMember struct {
	TenantKey string                                                `json:"tenant_key,omitempty"` // 租户 Key
	UserID    *EventV2IMChatUpdatedV1ModeratorListAddedMemberUserID `json:"user_id,omitempty"`    // 用户 ID
}

// EventV2IMChatUpdatedV1ModeratorListAddedMemberUserID ...
type EventV2IMChatUpdatedV1ModeratorListAddedMemberUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2IMChatUpdatedV1ModeratorListRemovedMember ...
type EventV2IMChatUpdatedV1ModeratorListRemovedMember struct {
	TenantKey string                                                  `json:"tenant_key,omitempty"` // 租户 Key
	UserID    *EventV2IMChatUpdatedV1ModeratorListRemovedMemberUserID `json:"user_id,omitempty"`    // 用户 ID
}

// EventV2IMChatUpdatedV1ModeratorListRemovedMemberUserID ...
type EventV2IMChatUpdatedV1ModeratorListRemovedMemberUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMMessageReactionCreatedV1
//
// 消息被添加某一个表情回复后触发此事件{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=message.reaction&event=created)
// 注意事项:
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)  ，具备[获取单聊、群组消息] 或 [获取与发送单聊、群组消息]权限，并订阅 [消息与群组] 分类下的 [消息被reaction] 事件才可接收推送
// - 机器人只能收到所在群聊内的消息被添加表情回复事件
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/events/created
func (r *EventCallbackService) HandlerEventV2IMMessageReactionCreatedV1(f EventV2IMMessageReactionCreatedV1Handler) {
	r.cli.eventHandler.eventV2IMMessageReactionCreatedV1Handler = f
}

// EventV2IMMessageReactionCreatedV1Handler event EventV2IMMessageReactionCreatedV1 handler
type EventV2IMMessageReactionCreatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMMessageReactionCreatedV1) (string, error)

// EventV2IMMessageReactionCreatedV1 ...
type EventV2IMMessageReactionCreatedV1 struct {
	MessageID    string                                         `json:"message_id,omitempty"`    // 消息的 open_message_id
	ReactionType *EventV2IMMessageReactionCreatedV1ReactionType `json:"reaction_type,omitempty"` // 表情回复的资源类型
	OperatorType string                                         `json:"operator_type,omitempty"` // 操作人类型
	UserID       *EventV2IMMessageReactionCreatedV1UserID       `json:"user_id,omitempty"`       // 用户 ID
	AppID        string                                         `json:"app_id,omitempty"`        // 应用 ID
	ActionTime   string                                         `json:"action_time,omitempty"`   // 添加表情回复时间戳（单位：ms）
}

// EventV2IMMessageReactionCreatedV1ReactionType ...
type EventV2IMMessageReactionCreatedV1ReactionType struct {
	EmojiType string `json:"emoji_type,omitempty"` // emoji类型 [emoji类型列举](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/emojis-introduce)
}

// EventV2IMMessageReactionCreatedV1UserID ...
type EventV2IMMessageReactionCreatedV1UserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMMessageReactionDeletedV1
//
// 消息被删除某一个表情回复后触发此事件{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=message.reaction&event=deleted)
// 注意事项:
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)  ，具备[获取单聊、群组消息] 或 [获取与发送单聊、群组消息]权限，并订阅 [消息与群组] 分类下的 [消息被取消reaction] 事件才可接收推送
// - 机器人只能收到所在群聊内的消息被删除表情回复事件
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/events/deleted
func (r *EventCallbackService) HandlerEventV2IMMessageReactionDeletedV1(f EventV2IMMessageReactionDeletedV1Handler) {
	r.cli.eventHandler.eventV2IMMessageReactionDeletedV1Handler = f
}

// EventV2IMMessageReactionDeletedV1Handler event EventV2IMMessageReactionDeletedV1 handler
type EventV2IMMessageReactionDeletedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMMessageReactionDeletedV1) (string, error)

// EventV2IMMessageReactionDeletedV1 ...
type EventV2IMMessageReactionDeletedV1 struct {
	MessageID    string                                         `json:"message_id,omitempty"`    // 消息的 open_message_id
	ReactionType *EventV2IMMessageReactionDeletedV1ReactionType `json:"reaction_type,omitempty"` // 表情回复的资源类型
	OperatorType string                                         `json:"operator_type,omitempty"` // 操作人类型
	UserID       *EventV2IMMessageReactionDeletedV1UserID       `json:"user_id,omitempty"`       // 用户 ID
	AppID        string                                         `json:"app_id,omitempty"`        // 应用 ID
	ActionTime   string                                         `json:"action_time,omitempty"`   // 表情回复被添加时的时间戳（单位：ms）
}

// EventV2IMMessageReactionDeletedV1ReactionType ...
type EventV2IMMessageReactionDeletedV1ReactionType struct {
	EmojiType string `json:"emoji_type,omitempty"` // emoji类型 [emoji类型列举](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message-reaction/emojis-introduce)
}

// EventV2IMMessageReactionDeletedV1UserID ...
type EventV2IMMessageReactionDeletedV1UserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMMessageReadV1
//
// 用户阅读机器人发送的单聊消息后触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=message&event=message_read)
// 注意事项:
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)
// - 需要订阅 [消息与群组] 分类下的 [消息已读] 事件
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/events/message_read
func (r *EventCallbackService) HandlerEventV2IMMessageReadV1(f EventV2IMMessageReadV1Handler) {
	r.cli.eventHandler.eventV2IMMessageReadV1Handler = f
}

// EventV2IMMessageReadV1Handler event EventV2IMMessageReadV1 handler
type EventV2IMMessageReadV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMMessageReadV1) (string, error)

// EventV2IMMessageReadV1 ...
type EventV2IMMessageReadV1 struct {
	Reader        *EventV2IMMessageReadV1Reader `json:"reader,omitempty"`          // -
	MessageIDList []string                      `json:"message_id_list,omitempty"` // 消息列表
}

// EventV2IMMessageReadV1Reader ...
type EventV2IMMessageReadV1Reader struct {
	ReaderID  *EventV2IMMessageReadV1ReaderReaderID `json:"reader_id,omitempty"`  // 用户 ID
	ReadTime  string                                `json:"read_time,omitempty"`  // 阅读时间
	TenantKey string                                `json:"tenant_key,omitempty"` // tenant key
}

// EventV2IMMessageReadV1ReaderReaderID ...
type EventV2IMMessageReadV1ReaderReaderID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2IMMessageReceiveV1
//
// 机器人接收到用户发送的消息后触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=im&version=v1&resource=message&event=receive)
// 注意事项:
// - 需要开启[机器人能力](https://open.feishu.cn/document/home/<USER>/create-an-app)  ，并订阅 [消息与群组] 分类下的 [接收消息v2.0] 事件才可接收推送
// - 同时，将根据应用具备的权限，判断可推送的信息：
// - 当具备[获取用户发给机器人的单聊消息]权限或者[读取用户发给机器人的单聊消息（历史权限）]，可接收与机器人单聊会话中用户发送的所有消息
// - 当具备[获取群组中所有消息] 权限时，可接收与机器人所在群聊会话中用户发送的所有消息
// - 当具备[获取用户在群组中@机器人的消息] 权限或者[获取用户在群聊中@机器人的消息（历史权限）]，可接收机器人所在群聊中用户 @ 机器人的消息
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/events/receive
func (r *EventCallbackService) HandlerEventV2IMMessageReceiveV1(f EventV2IMMessageReceiveV1Handler) {
	r.cli.eventHandler.eventV2IMMessageReceiveV1Handler = f
}

// EventV2IMMessageReceiveV1Handler event EventV2IMMessageReceiveV1 handler
type EventV2IMMessageReceiveV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2IMMessageReceiveV1) (string, error)

// EventV2IMMessageReceiveV1 ...
type EventV2IMMessageReceiveV1 struct {
	Sender  *EventV2IMMessageReceiveV1Sender  `json:"sender,omitempty"`  // 事件的发送者
	Message *EventV2IMMessageReceiveV1Message `json:"message,omitempty"` // 事件中包含的消息内容
}

// EventV2IMMessageReceiveV1Sender ...
type EventV2IMMessageReceiveV1Sender struct {
	SenderID   *EventV2IMMessageReceiveV1SenderSenderID `json:"sender_id,omitempty"`   // 用户 ID
	SenderType string                                   `json:"sender_type,omitempty"` // 消息发送者类型。目前只支持用户(user)发送的消息。
	TenantKey  string                                   `json:"tenant_key,omitempty"`  // tenant key
}

// EventV2IMMessageReceiveV1SenderSenderID ...
type EventV2IMMessageReceiveV1SenderSenderID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2IMMessageReceiveV1Message ...
type EventV2IMMessageReceiveV1Message struct {
	MessageID   string                                     `json:"message_id,omitempty"`   // 消息的 open_message_id
	RootID      string                                     `json:"root_id,omitempty"`      // 回复消息 根 id
	ParentID    string                                     `json:"parent_id,omitempty"`    // 回复消息 父 id
	CreateTime  string                                     `json:"create_time,omitempty"`  // 消息发送时间 毫秒
	ChatID      string                                     `json:"chat_id,omitempty"`      // 消息所在的群组 id
	ChatType    ChatMode                                   `json:"chat_type,omitempty"`    // 消息所在的群组类型，单聊（p2p）或群聊（group）
	MessageType MsgType                                    `json:"message_type,omitempty"` // 消息类型
	Content     string                                     `json:"content,omitempty"`      // 消息内容, json 格式 ,[各类型消息Content](https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/im-v1/message/events/message_content)
	Mentions    []*EventV2IMMessageReceiveV1MessageMention `json:"mentions,omitempty"`     // 被提及用户的信息
}

// EventV2IMMessageReceiveV1MessageMention ...
type EventV2IMMessageReceiveV1MessageMention struct {
	Key       string                                     `json:"key,omitempty"`        // mention key
	ID        *EventV2IMMessageReceiveV1MessageMentionID `json:"id,omitempty"`         // 用户 ID
	Name      string                                     `json:"name,omitempty"`       // 用户姓名
	TenantKey string                                     `json:"tenant_key,omitempty"` // tenant key
}

// EventV2IMMessageReceiveV1MessageMentionID ...
type EventV2IMMessageReceiveV1MessageMentionID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2MeetingRoomMeetingRoomCreatedV1
//
// 会议室被创建将触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=meeting_room&version=v1&resource=meeting_room&event=created)
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/meeting_room-v1/meeting_room/events/created
func (r *EventCallbackService) HandlerEventV2MeetingRoomMeetingRoomCreatedV1(f EventV2MeetingRoomMeetingRoomCreatedV1Handler) {
	r.cli.eventHandler.eventV2MeetingRoomMeetingRoomCreatedV1Handler = f
}

// EventV2MeetingRoomMeetingRoomCreatedV1Handler event EventV2MeetingRoomMeetingRoomCreatedV1 handler
type EventV2MeetingRoomMeetingRoomCreatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2MeetingRoomMeetingRoomCreatedV1) (string, error)

// EventV2MeetingRoomMeetingRoomCreatedV1 ...
type EventV2MeetingRoomMeetingRoomCreatedV1 struct {
	RoomName string `json:"room_name,omitempty"` // 会议室名称
	RoomID   string `json:"room_id,omitempty"`   // 会议室 ID
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2MeetingRoomMeetingRoomDeletedV1
//
// 会议室被删除将触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=meeting_room&version=v1&resource=meeting_room&event=deleted)
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/meeting_room-v1/meeting_room/events/deleted
func (r *EventCallbackService) HandlerEventV2MeetingRoomMeetingRoomDeletedV1(f EventV2MeetingRoomMeetingRoomDeletedV1Handler) {
	r.cli.eventHandler.eventV2MeetingRoomMeetingRoomDeletedV1Handler = f
}

// EventV2MeetingRoomMeetingRoomDeletedV1Handler event EventV2MeetingRoomMeetingRoomDeletedV1 handler
type EventV2MeetingRoomMeetingRoomDeletedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2MeetingRoomMeetingRoomDeletedV1) (string, error)

// EventV2MeetingRoomMeetingRoomDeletedV1 ...
type EventV2MeetingRoomMeetingRoomDeletedV1 struct {
	RoomName string `json:"room_name,omitempty"` // 会议室名称
	RoomID   string `json:"room_id,omitempty"`   // 会议室 ID
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2MeetingRoomMeetingRoomStatusChangedV1
//
// 会议室状态信息变更将触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=meeting_room&version=v1&resource=meeting_room&event=status_changed)
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/meeting_room-v1/meeting_room/events/status_changed
func (r *EventCallbackService) HandlerEventV2MeetingRoomMeetingRoomStatusChangedV1(f EventV2MeetingRoomMeetingRoomStatusChangedV1Handler) {
	r.cli.eventHandler.eventV2MeetingRoomMeetingRoomStatusChangedV1Handler = f
}

// EventV2MeetingRoomMeetingRoomStatusChangedV1Handler event EventV2MeetingRoomMeetingRoomStatusChangedV1 handler
type EventV2MeetingRoomMeetingRoomStatusChangedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2MeetingRoomMeetingRoomStatusChangedV1) (string, error)

// EventV2MeetingRoomMeetingRoomStatusChangedV1 ...
type EventV2MeetingRoomMeetingRoomStatusChangedV1 struct {
	RoomName string `json:"room_name,omitempty"` // 会议室名称
	RoomID   string `json:"room_id,omitempty"`   // 会议室 ID
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2MeetingRoomMeetingRoomUpdatedV1
//
// 会议室属性更新将触发此事件。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=meeting_room&version=v1&resource=meeting_room&event=updated)
// 了解事件订阅的使用场景和配置流程，请点击查看 [事件订阅概述](https://open.feishu.cn/document/ukTMukTMukTM/uUTNz4SN1MjL1UzM)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/meeting_room-v1/meeting_room/events/updated
func (r *EventCallbackService) HandlerEventV2MeetingRoomMeetingRoomUpdatedV1(f EventV2MeetingRoomMeetingRoomUpdatedV1Handler) {
	r.cli.eventHandler.eventV2MeetingRoomMeetingRoomUpdatedV1Handler = f
}

// EventV2MeetingRoomMeetingRoomUpdatedV1Handler event EventV2MeetingRoomMeetingRoomUpdatedV1 handler
type EventV2MeetingRoomMeetingRoomUpdatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2MeetingRoomMeetingRoomUpdatedV1) (string, error)

// EventV2MeetingRoomMeetingRoomUpdatedV1 ...
type EventV2MeetingRoomMeetingRoomUpdatedV1 struct {
	RoomName string `json:"room_name,omitempty"` // Meeting room name
	RoomID   string `json:"room_id,omitempty"`   // Meeting room ID
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2TaskTaskCommentUpdatedV1
//
// 当 APP 创建的任务评论信息发生变更时触发此事件，包括任务评论的创建、回复、更新、删除。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=task&version=v1&resource=task.comment&event=updated)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/task-v1/task-comment/events/updated
func (r *EventCallbackService) HandlerEventV2TaskTaskCommentUpdatedV1(f EventV2TaskTaskCommentUpdatedV1Handler) {
	r.cli.eventHandler.eventV2TaskTaskCommentUpdatedV1Handler = f
}

// EventV2TaskTaskCommentUpdatedV1Handler event EventV2TaskTaskCommentUpdatedV1 handler
type EventV2TaskTaskCommentUpdatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2TaskTaskCommentUpdatedV1) (string, error)

// EventV2TaskTaskCommentUpdatedV1 ...
type EventV2TaskTaskCommentUpdatedV1 struct {
	TaskID    string `json:"task_id,omitempty"`    // 任务ID
	CommentID string `json:"comment_id,omitempty"` // 任务评论ID
	ParentID  string `json:"parent_id,omitempty"`  // 任务评论父ID
	ObjType   int64  `json:"obj_type,omitempty"`   // 通知类型（1：创建评论，2：回复评论，3：更新评论，4：删除评论）
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2TaskTaskUpdateTenantV1
//
// APP 订阅此事件后可接收到该 APP 所在租户的所有来源接口创建的任务的变更事件。事件体为发生变更任务的相关用户的 open_id，可用此 open_id ，通过 获取任务列表接口获取与该用户相关的所有任务。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=task&version=v1&resource=task&event=update_tenant)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/task-v1/task/events/update_tenant
func (r *EventCallbackService) HandlerEventV2TaskTaskUpdateTenantV1(f EventV2TaskTaskUpdateTenantV1Handler) {
	r.cli.eventHandler.eventV2TaskTaskUpdateTenantV1Handler = f
}

// EventV2TaskTaskUpdateTenantV1Handler event EventV2TaskTaskUpdateTenantV1 handler
type EventV2TaskTaskUpdateTenantV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2TaskTaskUpdateTenantV1) (string, error)

// EventV2TaskTaskUpdateTenantV1 ...
type EventV2TaskTaskUpdateTenantV1 struct {
	UserIDList *EventV2TaskTaskUpdateTenantV1UserIDList `json:"user_id_list,omitempty"` // 用户 ID 列表
}

// EventV2TaskTaskUpdateTenantV1UserIDList ...
type EventV2TaskTaskUpdateTenantV1UserIDList struct {
	UserIDList []*EventV2TaskTaskUpdateTenantV1UserIDListUserID `json:"user_id_list,omitempty"` // 用户 ID 列表
}

// EventV2TaskTaskUpdateTenantV1UserIDListUserID ...
type EventV2TaskTaskUpdateTenantV1UserIDListUserID struct {
	UnionID string `json:"union_id,omitempty"` // 忽略此字段
	UserID  string `json:"user_id,omitempty"`  // 忽略此字段
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open_id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2TaskTaskUpdatedV1
//
// 当 APP 订阅此事件后可以接收到由该 APP 创建的任务发生的变更，包括任务标题、描述、截止时间、协作者、关注者、提醒时间、状态（完成或取消完成）。{使用示例}(url=/api/tools/api_explore/api_explore_config?project=task&version=v1&resource=task&event=updated)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/task-v1/task/events/updated
func (r *EventCallbackService) HandlerEventV2TaskTaskUpdatedV1(f EventV2TaskTaskUpdatedV1Handler) {
	r.cli.eventHandler.eventV2TaskTaskUpdatedV1Handler = f
}

// EventV2TaskTaskUpdatedV1Handler event EventV2TaskTaskUpdatedV1 handler
type EventV2TaskTaskUpdatedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2TaskTaskUpdatedV1) (string, error)

// EventV2TaskTaskUpdatedV1 ...
type EventV2TaskTaskUpdatedV1 struct {
	TaskID  string `json:"task_id,omitempty"`  // 任务ID
	ObjType int64  `json:"obj_type,omitempty"` // 通知类型（1：任务详情发生变化，2：任务协作者发生变化，3：任务关注者发生变化，4：任务提醒时间发生变化，5：任务完成，6：任务取消完成，7：任务删除）
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2VCMeetingJoinMeetingV1
//
// 发生在有人加入会议时
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/vc-v1/meeting/events/join_meeting
func (r *EventCallbackService) HandlerEventV2VCMeetingJoinMeetingV1(f EventV2VCMeetingJoinMeetingV1Handler) {
	r.cli.eventHandler.eventV2VCMeetingJoinMeetingV1Handler = f
}

// EventV2VCMeetingJoinMeetingV1Handler event EventV2VCMeetingJoinMeetingV1 handler
type EventV2VCMeetingJoinMeetingV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2VCMeetingJoinMeetingV1) (string, error)

// EventV2VCMeetingJoinMeetingV1 ...
type EventV2VCMeetingJoinMeetingV1 struct {
	Meeting  *EventV2VCMeetingJoinMeetingV1Meeting  `json:"meeting,omitempty"`  // 会议数据
	Operator *EventV2VCMeetingJoinMeetingV1Operator `json:"operator,omitempty"` // 事件操作人
}

// EventV2VCMeetingJoinMeetingV1Meeting ...
type EventV2VCMeetingJoinMeetingV1Meeting struct {
	ID        string                                        `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     string                                        `json:"topic,omitempty"`      // 会议主题
	MeetingNo string                                        `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	StartTime string                                        `json:"start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	EndTime   string                                        `json:"end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	HostUser  *EventV2VCMeetingJoinMeetingV1MeetingHostUser `json:"host_user,omitempty"`  // 会议主持人
	Owner     *EventV2VCMeetingJoinMeetingV1MeetingOwner    `json:"owner,omitempty"`      // 会议拥有者
}

// EventV2VCMeetingJoinMeetingV1MeetingHostUser ...
type EventV2VCMeetingJoinMeetingV1MeetingHostUser struct {
	ID       *EventV2VCMeetingJoinMeetingV1MeetingHostUserID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                           `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                           `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingJoinMeetingV1MeetingHostUserID ...
type EventV2VCMeetingJoinMeetingV1MeetingHostUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingJoinMeetingV1MeetingOwner ...
type EventV2VCMeetingJoinMeetingV1MeetingOwner struct {
	ID       *EventV2VCMeetingJoinMeetingV1MeetingOwnerID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                        `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                        `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingJoinMeetingV1MeetingOwnerID ...
type EventV2VCMeetingJoinMeetingV1MeetingOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingJoinMeetingV1Operator ...
type EventV2VCMeetingJoinMeetingV1Operator struct {
	ID       *EventV2VCMeetingJoinMeetingV1OperatorID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                    `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                    `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingJoinMeetingV1OperatorID ...
type EventV2VCMeetingJoinMeetingV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2VCMeetingLeaveMeetingV1
//
// 发生在有人离开会议时{使用示例}(url=/api/tools/api_explore/api_explore_config?project=vc&version=v1&resource=meeting&event=leave_meeting)
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/vc-v1/meeting/events/leave_meeting
func (r *EventCallbackService) HandlerEventV2VCMeetingLeaveMeetingV1(f EventV2VCMeetingLeaveMeetingV1Handler) {
	r.cli.eventHandler.eventV2VCMeetingLeaveMeetingV1Handler = f
}

// EventV2VCMeetingLeaveMeetingV1Handler event EventV2VCMeetingLeaveMeetingV1 handler
type EventV2VCMeetingLeaveMeetingV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2VCMeetingLeaveMeetingV1) (string, error)

// EventV2VCMeetingLeaveMeetingV1 ...
type EventV2VCMeetingLeaveMeetingV1 struct {
	Meeting     *EventV2VCMeetingLeaveMeetingV1Meeting  `json:"meeting,omitempty"`      // 会议数据
	Operator    *EventV2VCMeetingLeaveMeetingV1Operator `json:"operator,omitempty"`     // 事件操作人
	LeaveReason int64                                   `json:"leave_reason,omitempty"` // 离开会议原因, 可选值有: `1`：主动离会, `2`：会议结束, `3`：被踢出
}

// EventV2VCMeetingLeaveMeetingV1Meeting ...
type EventV2VCMeetingLeaveMeetingV1Meeting struct {
	ID        string                                         `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     string                                         `json:"topic,omitempty"`      // 会议主题
	MeetingNo string                                         `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	StartTime string                                         `json:"start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	EndTime   string                                         `json:"end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	HostUser  *EventV2VCMeetingLeaveMeetingV1MeetingHostUser `json:"host_user,omitempty"`  // 会议主持人
	Owner     *EventV2VCMeetingLeaveMeetingV1MeetingOwner    `json:"owner,omitempty"`      // 会议拥有者
}

// EventV2VCMeetingLeaveMeetingV1MeetingHostUser ...
type EventV2VCMeetingLeaveMeetingV1MeetingHostUser struct {
	ID       *EventV2VCMeetingLeaveMeetingV1MeetingHostUserID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                            `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                            `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingLeaveMeetingV1MeetingHostUserID ...
type EventV2VCMeetingLeaveMeetingV1MeetingHostUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求:  获取用户 userid
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingLeaveMeetingV1MeetingOwner ...
type EventV2VCMeetingLeaveMeetingV1MeetingOwner struct {
	ID       *EventV2VCMeetingLeaveMeetingV1MeetingOwnerID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                         `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                         `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingLeaveMeetingV1MeetingOwnerID ...
type EventV2VCMeetingLeaveMeetingV1MeetingOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求:  获取用户 userid
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingLeaveMeetingV1Operator ...
type EventV2VCMeetingLeaveMeetingV1Operator struct {
	ID       *EventV2VCMeetingLeaveMeetingV1OperatorID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                     `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                     `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingLeaveMeetingV1OperatorID ...
type EventV2VCMeetingLeaveMeetingV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求:  获取用户 userid
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2VCMeetingMeetingEndedV1
//
// 发生在会议结束时
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/vc-v1/meeting/events/meeting_ended
func (r *EventCallbackService) HandlerEventV2VCMeetingMeetingEndedV1(f EventV2VCMeetingMeetingEndedV1Handler) {
	r.cli.eventHandler.eventV2VCMeetingMeetingEndedV1Handler = f
}

// EventV2VCMeetingMeetingEndedV1Handler event EventV2VCMeetingMeetingEndedV1 handler
type EventV2VCMeetingMeetingEndedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2VCMeetingMeetingEndedV1) (string, error)

// EventV2VCMeetingMeetingEndedV1 ...
type EventV2VCMeetingMeetingEndedV1 struct {
	Meeting  *EventV2VCMeetingMeetingEndedV1Meeting  `json:"meeting,omitempty"`  // 会议数据
	Operator *EventV2VCMeetingMeetingEndedV1Operator `json:"operator,omitempty"` // 事件操作人
}

// EventV2VCMeetingMeetingEndedV1Meeting ...
type EventV2VCMeetingMeetingEndedV1Meeting struct {
	ID        string                                         `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     string                                         `json:"topic,omitempty"`      // 会议主题
	MeetingNo string                                         `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	StartTime string                                         `json:"start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	EndTime   string                                         `json:"end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	HostUser  *EventV2VCMeetingMeetingEndedV1MeetingHostUser `json:"host_user,omitempty"`  // 会议主持人
	Owner     *EventV2VCMeetingMeetingEndedV1MeetingOwner    `json:"owner,omitempty"`      // 会议拥有者
}

// EventV2VCMeetingMeetingEndedV1MeetingHostUser ...
type EventV2VCMeetingMeetingEndedV1MeetingHostUser struct {
	ID       *EventV2VCMeetingMeetingEndedV1MeetingHostUserID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                            `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                            `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingMeetingEndedV1MeetingHostUserID ...
type EventV2VCMeetingMeetingEndedV1MeetingHostUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingMeetingEndedV1MeetingOwner ...
type EventV2VCMeetingMeetingEndedV1MeetingOwner struct {
	ID       *EventV2VCMeetingMeetingEndedV1MeetingOwnerID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                         `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                         `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingMeetingEndedV1MeetingOwnerID ...
type EventV2VCMeetingMeetingEndedV1MeetingOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingMeetingEndedV1Operator ...
type EventV2VCMeetingMeetingEndedV1Operator struct {
	ID       *EventV2VCMeetingMeetingEndedV1OperatorID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                     `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                     `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingMeetingEndedV1OperatorID ...
type EventV2VCMeetingMeetingEndedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2VCMeetingMeetingStartedV1
//
// 发生在会议开始时
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/vc-v1/meeting/events/meeting_started
func (r *EventCallbackService) HandlerEventV2VCMeetingMeetingStartedV1(f EventV2VCMeetingMeetingStartedV1Handler) {
	r.cli.eventHandler.eventV2VCMeetingMeetingStartedV1Handler = f
}

// EventV2VCMeetingMeetingStartedV1Handler event EventV2VCMeetingMeetingStartedV1 handler
type EventV2VCMeetingMeetingStartedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2VCMeetingMeetingStartedV1) (string, error)

// EventV2VCMeetingMeetingStartedV1 ...
type EventV2VCMeetingMeetingStartedV1 struct {
	Meeting  *EventV2VCMeetingMeetingStartedV1Meeting  `json:"meeting,omitempty"`  // 会议数据
	Operator *EventV2VCMeetingMeetingStartedV1Operator `json:"operator,omitempty"` // 事件操作人
}

// EventV2VCMeetingMeetingStartedV1Meeting ...
type EventV2VCMeetingMeetingStartedV1Meeting struct {
	ID        string                                           `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     string                                           `json:"topic,omitempty"`      // 会议主题
	MeetingNo string                                           `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	StartTime string                                           `json:"start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	EndTime   string                                           `json:"end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	HostUser  *EventV2VCMeetingMeetingStartedV1MeetingHostUser `json:"host_user,omitempty"`  // 会议主持人
	Owner     *EventV2VCMeetingMeetingStartedV1MeetingOwner    `json:"owner,omitempty"`      // 会议拥有者
}

// EventV2VCMeetingMeetingStartedV1MeetingHostUser ...
type EventV2VCMeetingMeetingStartedV1MeetingHostUser struct {
	ID       *EventV2VCMeetingMeetingStartedV1MeetingHostUserID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                              `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                              `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingMeetingStartedV1MeetingHostUserID ...
type EventV2VCMeetingMeetingStartedV1MeetingHostUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingMeetingStartedV1MeetingOwner ...
type EventV2VCMeetingMeetingStartedV1MeetingOwner struct {
	ID       *EventV2VCMeetingMeetingStartedV1MeetingOwnerID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                           `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                           `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingMeetingStartedV1MeetingOwnerID ...
type EventV2VCMeetingMeetingStartedV1MeetingOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingMeetingStartedV1Operator ...
type EventV2VCMeetingMeetingStartedV1Operator struct {
	ID       *EventV2VCMeetingMeetingStartedV1OperatorID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                       `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                       `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingMeetingStartedV1OperatorID ...
type EventV2VCMeetingMeetingStartedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2VCMeetingRecordingEndedV1
//
// 发生在录制结束时
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/vc-v1/meeting/events/recording_ended
func (r *EventCallbackService) HandlerEventV2VCMeetingRecordingEndedV1(f EventV2VCMeetingRecordingEndedV1Handler) {
	r.cli.eventHandler.eventV2VCMeetingRecordingEndedV1Handler = f
}

// EventV2VCMeetingRecordingEndedV1Handler event EventV2VCMeetingRecordingEndedV1 handler
type EventV2VCMeetingRecordingEndedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2VCMeetingRecordingEndedV1) (string, error)

// EventV2VCMeetingRecordingEndedV1 ...
type EventV2VCMeetingRecordingEndedV1 struct {
	Meeting  *EventV2VCMeetingRecordingEndedV1Meeting  `json:"meeting,omitempty"`  // 会议数据
	Operator *EventV2VCMeetingRecordingEndedV1Operator `json:"operator,omitempty"` // 事件操作人
}

// EventV2VCMeetingRecordingEndedV1Meeting ...
type EventV2VCMeetingRecordingEndedV1Meeting struct {
	ID        string                                           `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     string                                           `json:"topic,omitempty"`      // 会议主题
	MeetingNo string                                           `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	StartTime string                                           `json:"start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	EndTime   string                                           `json:"end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	HostUser  *EventV2VCMeetingRecordingEndedV1MeetingHostUser `json:"host_user,omitempty"`  // 会议主持人
	Owner     *EventV2VCMeetingRecordingEndedV1MeetingOwner    `json:"owner,omitempty"`      // 会议拥有者
}

// EventV2VCMeetingRecordingEndedV1MeetingHostUser ...
type EventV2VCMeetingRecordingEndedV1MeetingHostUser struct {
	ID       *EventV2VCMeetingRecordingEndedV1MeetingHostUserID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                              `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                              `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingRecordingEndedV1MeetingHostUserID ...
type EventV2VCMeetingRecordingEndedV1MeetingHostUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingRecordingEndedV1MeetingOwner ...
type EventV2VCMeetingRecordingEndedV1MeetingOwner struct {
	ID       *EventV2VCMeetingRecordingEndedV1MeetingOwnerID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                           `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                           `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingRecordingEndedV1MeetingOwnerID ...
type EventV2VCMeetingRecordingEndedV1MeetingOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingRecordingEndedV1Operator ...
type EventV2VCMeetingRecordingEndedV1Operator struct {
	ID       *EventV2VCMeetingRecordingEndedV1OperatorID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                       `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                       `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingRecordingEndedV1OperatorID ...
type EventV2VCMeetingRecordingEndedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2VCMeetingRecordingReadyV1
//
// 发生在录制文件上传完毕时
// 收到该事件后，方可进行录制文件获取、授权等操作
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/vc-v1/meeting/events/recording_ready
func (r *EventCallbackService) HandlerEventV2VCMeetingRecordingReadyV1(f EventV2VCMeetingRecordingReadyV1Handler) {
	r.cli.eventHandler.eventV2VCMeetingRecordingReadyV1Handler = f
}

// EventV2VCMeetingRecordingReadyV1Handler event EventV2VCMeetingRecordingReadyV1 handler
type EventV2VCMeetingRecordingReadyV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2VCMeetingRecordingReadyV1) (string, error)

// EventV2VCMeetingRecordingReadyV1 ...
type EventV2VCMeetingRecordingReadyV1 struct {
	Meeting  *EventV2VCMeetingRecordingReadyV1Meeting `json:"meeting,omitempty"`  // 会议数据
	URL      string                                   `json:"url,omitempty"`      // 会议录制链接
	Duration string                                   `json:"duration,omitempty"` // 录制总时长（单位msec）
}

// EventV2VCMeetingRecordingReadyV1Meeting ...
type EventV2VCMeetingRecordingReadyV1Meeting struct {
	ID        string                                        `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     string                                        `json:"topic,omitempty"`      // 会议主题
	MeetingNo string                                        `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	Owner     *EventV2VCMeetingRecordingReadyV1MeetingOwner `json:"owner,omitempty"`      // 会议拥有者
}

// EventV2VCMeetingRecordingReadyV1MeetingOwner ...
type EventV2VCMeetingRecordingReadyV1MeetingOwner struct {
	ID *EventV2VCMeetingRecordingReadyV1MeetingOwnerID `json:"id,omitempty"` // 用户 ID
}

// EventV2VCMeetingRecordingReadyV1MeetingOwnerID ...
type EventV2VCMeetingRecordingReadyV1MeetingOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2VCMeetingRecordingStartedV1
//
// 发生在开始录制时
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/vc-v1/meeting/events/recording_started
func (r *EventCallbackService) HandlerEventV2VCMeetingRecordingStartedV1(f EventV2VCMeetingRecordingStartedV1Handler) {
	r.cli.eventHandler.eventV2VCMeetingRecordingStartedV1Handler = f
}

// EventV2VCMeetingRecordingStartedV1Handler event EventV2VCMeetingRecordingStartedV1 handler
type EventV2VCMeetingRecordingStartedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2VCMeetingRecordingStartedV1) (string, error)

// EventV2VCMeetingRecordingStartedV1 ...
type EventV2VCMeetingRecordingStartedV1 struct {
	Meeting  *EventV2VCMeetingRecordingStartedV1Meeting  `json:"meeting,omitempty"`  // 会议数据
	Operator *EventV2VCMeetingRecordingStartedV1Operator `json:"operator,omitempty"` // 事件操作人
}

// EventV2VCMeetingRecordingStartedV1Meeting ...
type EventV2VCMeetingRecordingStartedV1Meeting struct {
	ID        string                                             `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     string                                             `json:"topic,omitempty"`      // 会议主题
	MeetingNo string                                             `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	StartTime string                                             `json:"start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	EndTime   string                                             `json:"end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	HostUser  *EventV2VCMeetingRecordingStartedV1MeetingHostUser `json:"host_user,omitempty"`  // 会议主持人
	Owner     *EventV2VCMeetingRecordingStartedV1MeetingOwner    `json:"owner,omitempty"`      // 会议拥有者
}

// EventV2VCMeetingRecordingStartedV1MeetingHostUser ...
type EventV2VCMeetingRecordingStartedV1MeetingHostUser struct {
	ID       *EventV2VCMeetingRecordingStartedV1MeetingHostUserID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                                `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                                `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingRecordingStartedV1MeetingHostUserID ...
type EventV2VCMeetingRecordingStartedV1MeetingHostUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingRecordingStartedV1MeetingOwner ...
type EventV2VCMeetingRecordingStartedV1MeetingOwner struct {
	ID       *EventV2VCMeetingRecordingStartedV1MeetingOwnerID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                             `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                             `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingRecordingStartedV1MeetingOwnerID ...
type EventV2VCMeetingRecordingStartedV1MeetingOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingRecordingStartedV1Operator ...
type EventV2VCMeetingRecordingStartedV1Operator struct {
	ID       *EventV2VCMeetingRecordingStartedV1OperatorID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                         `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                         `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingRecordingStartedV1OperatorID ...
type EventV2VCMeetingRecordingStartedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2VCMeetingShareEndedV1
//
// 发生在屏幕共享结束时
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/vc-v1/meeting/events/share_ended
func (r *EventCallbackService) HandlerEventV2VCMeetingShareEndedV1(f EventV2VCMeetingShareEndedV1Handler) {
	r.cli.eventHandler.eventV2VCMeetingShareEndedV1Handler = f
}

// EventV2VCMeetingShareEndedV1Handler event EventV2VCMeetingShareEndedV1 handler
type EventV2VCMeetingShareEndedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2VCMeetingShareEndedV1) (string, error)

// EventV2VCMeetingShareEndedV1 ...
type EventV2VCMeetingShareEndedV1 struct {
	Meeting  *EventV2VCMeetingShareEndedV1Meeting  `json:"meeting,omitempty"`  // 会议数据
	Operator *EventV2VCMeetingShareEndedV1Operator `json:"operator,omitempty"` // 事件操作人
}

// EventV2VCMeetingShareEndedV1Meeting ...
type EventV2VCMeetingShareEndedV1Meeting struct {
	ID        string                                       `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     string                                       `json:"topic,omitempty"`      // 会议主题
	MeetingNo string                                       `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	StartTime string                                       `json:"start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	EndTime   string                                       `json:"end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	HostUser  *EventV2VCMeetingShareEndedV1MeetingHostUser `json:"host_user,omitempty"`  // 会议主持人
	Owner     *EventV2VCMeetingShareEndedV1MeetingOwner    `json:"owner,omitempty"`      // 会议拥有者
}

// EventV2VCMeetingShareEndedV1MeetingHostUser ...
type EventV2VCMeetingShareEndedV1MeetingHostUser struct {
	ID       *EventV2VCMeetingShareEndedV1MeetingHostUserID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                          `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                          `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingShareEndedV1MeetingHostUserID ...
type EventV2VCMeetingShareEndedV1MeetingHostUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingShareEndedV1MeetingOwner ...
type EventV2VCMeetingShareEndedV1MeetingOwner struct {
	ID       *EventV2VCMeetingShareEndedV1MeetingOwnerID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                       `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                       `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingShareEndedV1MeetingOwnerID ...
type EventV2VCMeetingShareEndedV1MeetingOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingShareEndedV1Operator ...
type EventV2VCMeetingShareEndedV1Operator struct {
	ID       *EventV2VCMeetingShareEndedV1OperatorID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                   `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                   `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingShareEndedV1OperatorID ...
type EventV2VCMeetingShareEndedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// Code generated by lark_sdk_gen. DO NOT EDIT.

// EventV2VCMeetingShareStartedV1
//
// 发生在屏幕共享开始时
//
// doc: https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/vc-v1/meeting/events/share_started
func (r *EventCallbackService) HandlerEventV2VCMeetingShareStartedV1(f EventV2VCMeetingShareStartedV1Handler) {
	r.cli.eventHandler.eventV2VCMeetingShareStartedV1Handler = f
}

// EventV2VCMeetingShareStartedV1Handler event EventV2VCMeetingShareStartedV1 handler
type EventV2VCMeetingShareStartedV1Handler func(ctx context.Context, cli *Lark, schema string, header *EventHeaderV2, event *EventV2VCMeetingShareStartedV1) (string, error)

// EventV2VCMeetingShareStartedV1 ...
type EventV2VCMeetingShareStartedV1 struct {
	Meeting  *EventV2VCMeetingShareStartedV1Meeting  `json:"meeting,omitempty"`  // 会议数据
	Operator *EventV2VCMeetingShareStartedV1Operator `json:"operator,omitempty"` // 事件操作人
}

// EventV2VCMeetingShareStartedV1Meeting ...
type EventV2VCMeetingShareStartedV1Meeting struct {
	ID        string                                         `json:"id,omitempty"`         // 会议ID（视频会议的唯一标识，视频会议开始后才会产生）
	Topic     string                                         `json:"topic,omitempty"`      // 会议主题
	MeetingNo string                                         `json:"meeting_no,omitempty"` // 9位会议号（飞书用户可通过输入9位会议号快捷入会）
	StartTime string                                         `json:"start_time,omitempty"` // 会议开始时间（unix时间，单位sec）
	EndTime   string                                         `json:"end_time,omitempty"`   // 会议结束时间（unix时间，单位sec）
	HostUser  *EventV2VCMeetingShareStartedV1MeetingHostUser `json:"host_user,omitempty"`  // 会议主持人
	Owner     *EventV2VCMeetingShareStartedV1MeetingOwner    `json:"owner,omitempty"`      // 会议拥有者
}

// EventV2VCMeetingShareStartedV1MeetingHostUser ...
type EventV2VCMeetingShareStartedV1MeetingHostUser struct {
	ID       *EventV2VCMeetingShareStartedV1MeetingHostUserID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                            `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                            `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingShareStartedV1MeetingHostUserID ...
type EventV2VCMeetingShareStartedV1MeetingHostUserID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingShareStartedV1MeetingOwner ...
type EventV2VCMeetingShareStartedV1MeetingOwner struct {
	ID       *EventV2VCMeetingShareStartedV1MeetingOwnerID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                         `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                         `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingShareStartedV1MeetingOwnerID ...
type EventV2VCMeetingShareStartedV1MeetingOwnerID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}

// EventV2VCMeetingShareStartedV1Operator ...
type EventV2VCMeetingShareStartedV1Operator struct {
	ID       *EventV2VCMeetingShareStartedV1OperatorID `json:"id,omitempty"`        // 用户 ID
	UserRole int64                                     `json:"user_role,omitempty"` // 用户会中角色, 可选值有: `1`：普通参会人, `2`：主持人, `3`：联席主持人
	UserType int64                                     `json:"user_type,omitempty"` // 用户类型, 可选值有: `1`：lark用户, `2`：rooms用户, `3`：文档用户, `4`：neo单品用户, `5`：neo单品游客用户, `6`：pstn用户, `7`：sip用户
}

// EventV2VCMeetingShareStartedV1OperatorID ...
type EventV2VCMeetingShareStartedV1OperatorID struct {
	UnionID string `json:"union_id,omitempty"` // 用户的 union id
	UserID  string `json:"user_id,omitempty"`  // 用户的 user id, 字段权限要求: 获取用户 user ID
	OpenID  string `json:"open_id,omitempty"`  // 用户的 open id
}
